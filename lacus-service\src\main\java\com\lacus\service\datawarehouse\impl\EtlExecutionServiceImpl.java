package com.lacus.service.datawarehouse.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lacus.dao.datawarehouse.entity.EtlExecutionHistoryEntity;
import com.lacus.dao.datawarehouse.mapper.EtlExecutionHistoryMapper;
import com.lacus.service.datawarehouse.EtlExecutionService;
import com.lacus.service.datawarehouse.command.EtlTaskAddCommand;
import com.lacus.service.datawarehouse.model.EtlTaskModel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lacus.service.datawarehouse.util.EtlSqlGenerator;
import com.lacus.service.datawarehouse.engine.DorisExecutionEngine;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
@RequiredArgsConstructor
public class EtlExecutionServiceImpl extends ServiceImpl<EtlExecutionHistoryMapper, EtlExecutionHistoryEntity> implements EtlExecutionService {

    @Autowired
    private JdbcTemplate dorisJdbcTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private EtlSqlGenerator etlSqlGenerator;

    @Autowired
    private DorisExecutionEngine dorisExecutionEngine;

    private final EtlExecutionHistoryMapper executionHistoryMapper;

    // 运行中的任务缓存
    private final Map<Long, String> runningTasks = new ConcurrentHashMap<>();

    @Override
    @Async("etlExecutor")
    public String submitTask(EtlTaskModel model) {
        String executionId = UUID.randomUUID().toString();

        try {
            log.info("提交ETL任务执行，任务ID: {}, 执行ID: {}", model.getTaskId(), executionId);

            // 检查任务是否已在运行
            if (isTaskRunning(model.getTaskId())) {
                throw new RuntimeException("任务正在运行中，请勿重复提交");
            }

            // 标记任务为运行中
            runningTasks.put(model.getTaskId(), executionId);

            // 创建执行历史记录
            EtlExecutionHistoryEntity history = createExecutionHistory(model, executionId);

            // 异步执行ETL任务
            CompletableFuture.runAsync(() -> executeEtlTask(model, history));

            return executionId;

        } catch (Exception e) {
            log.error("提交ETL任务失败，任务ID: {}", model.getTaskId(), e);
            runningTasks.remove(model.getTaskId());
            throw new RuntimeException("提交ETL任务失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void stopTask(Long taskId) {
        try {
            log.info("停止ETL任务，任务ID: {}", taskId);

            String executionId = runningTasks.get(taskId);
            if (executionId == null) {
                log.warn("任务未在运行中，任务ID: {}", taskId);
                return;
            }

            // 更新执行历史状态
            EtlExecutionHistoryEntity history = executionHistoryMapper.selectByExecutionId(executionId);
            if (history != null && "RUNNING".equals(history.getStatus())) {
                history.setStatus("STOPPED");
                history.setExecutionStatus("STOPPED");
                history.setEndTime(new java.util.Date());
                history.setErrorMessage("用户手动停止");
                executionHistoryMapper.updateById(history);
            }

            // 移除运行中标记
            runningTasks.remove(taskId);

            log.info("ETL任务停止成功，任务ID: {}", taskId);

        } catch (Exception e) {
            log.error("停止ETL任务失败，任务ID: {}", taskId, e);
            throw new RuntimeException("停止ETL任务失败: " + e.getMessage());
        }
    }

    @Override
    public boolean isTaskRunning(Long taskId) {
        return runningTasks.containsKey(taskId);
    }

    @Override
    public Object previewResult(EtlTaskAddCommand command) {
        try {
            log.info("预览ETL结果，源层级: {}, 目标层级: {}", command.getSourceLayer(), command.getTargetLayer());

            // 预览功能使用宽松的验证
            validatePreviewConfig(command);

            // 生成预览SQL
            String previewSql = generatePreviewSql(command);

            // 执行预览查询（限制返回行数）
            List<Map<String, Object>> previewData = dorisJdbcTemplate.queryForList(previewSql + " LIMIT 100");

            // 构建预览结果
            Map<String, Object> result = new HashMap<>();
            result.put("previewData", previewData);
            result.put("totalColumns", previewData.isEmpty() ? 0 : previewData.get(0).size());
            result.put("previewRows", previewData.size());
            result.put("generatedSql", previewSql);
            result.put("previewTime", LocalDateTime.now());

            log.info("ETL预览完成，返回{}行数据", previewData.size());
            return result;

        } catch (Exception e) {
            log.error("预览ETL结果失败", e);
            throw new RuntimeException("预览ETL结果失败: " + e.getMessage());
        }
    }

    /**
     * 验证预览配置（宽松验证）
     */
    private boolean validatePreviewConfig(EtlTaskAddCommand command) {
        try {
            // 验证基本信息
            if (command.getSourceLayer() == null || command.getTargetLayer() == null) {
                throw new RuntimeException("源层级和目标层级不能为空");
            }

            // 检查是否有源表配置，如果没有则提示用户
            if (command.getSourceTablesConfig() == null || command.getSourceTablesConfig().trim().isEmpty()) {
                throw new RuntimeException("预览功能需要指定源表，请先配置源表信息");
            }

            log.info("预览配置验证通过");
            return true;

        } catch (Exception e) {
            log.error("预览配置验证失败", e);
            throw new RuntimeException("预览配置验证失败: " + e.getMessage());
        }
    }

    @Override
    public Object getFieldMappingSuggestions(String sourceLayer, String targetLayer, List<String> sourceTables) {
        try {
            log.info("获取字段映射建议，源层级: {}, 目标层级: {}, 源表: {}", sourceLayer, targetLayer, sourceTables);

            List<Map<String, Object>> suggestions = new ArrayList<>();

            for (String sourceTable : sourceTables) {
                // 获取源表字段信息
                List<Map<String, Object>> sourceFields = getTableFields(sourceLayer, sourceTable);

                // 生成目标表建议
                String suggestedTargetTable = generateTargetTableName(sourceTable, sourceLayer, targetLayer);
                List<Map<String, Object>> targetFields = generateTargetFields(sourceFields, targetLayer);

                Map<String, Object> tableSuggestion = new HashMap<>();
                tableSuggestion.put("sourceTable", sourceTable);
                tableSuggestion.put("suggestedTargetTable", suggestedTargetTable);
                tableSuggestion.put("sourceFields", sourceFields);
                tableSuggestion.put("suggestedTargetFields", targetFields);
                tableSuggestion.put("fieldMappings", generateFieldMappings(sourceFields, targetFields));

                suggestions.add(tableSuggestion);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("suggestions", suggestions);
            result.put("sourceLayer", sourceLayer);
            result.put("targetLayer", targetLayer);
            result.put("generateTime", LocalDateTime.now());

            return result;

        } catch (Exception e) {
            log.error("获取字段映射建议失败", e);
            throw new RuntimeException("获取字段映射建议失败: " + e.getMessage());
        }
    }

    @Override
    public String getExecutionLog(String executionId) {
        try {
            EtlExecutionHistoryEntity history = executionHistoryMapper.selectByExecutionId(executionId);
            if (history == null) {
                return "执行记录不存在";
            }

            StringBuilder logBuilder = new StringBuilder();
            logBuilder.append("=== ETL任务执行日志 ===\n");
            logBuilder.append("执行ID: ").append(executionId).append("\n");
            logBuilder.append("任务名称: ").append(history.getTaskName()).append("\n");
            logBuilder.append("开始时间: ").append(history.getStartTime()).append("\n");
            logBuilder.append("结束时间: ").append(history.getEndTime()).append("\n");
            logBuilder.append("执行状态: ").append(history.getExecutionStatus()).append("\n");
            logBuilder.append("处理行数: ").append(history.getProcessedRows()).append("\n");
            logBuilder.append("执行耗时: ").append(history.getExecutionTime()).append("ms\n");

            if (history.getErrorMessage() != null) {
                logBuilder.append("错误信息: ").append(history.getErrorMessage()).append("\n");
            }

            logBuilder.append("=== 详细日志 ===\n");
            if (history.getExecutionLog() != null) {
                logBuilder.append(history.getExecutionLog());
            } else {
                logBuilder.append("暂无详细日志");
            }

            return logBuilder.toString();

        } catch (Exception e) {
            log.error("获取执行日志失败，执行ID: {}", executionId, e);
            return "获取执行日志失败: " + e.getMessage();
        }
    }

    @Override
    public Object getExecutionMetrics(String executionId) {
        try {
            EtlExecutionHistoryEntity history = executionHistoryMapper.selectByExecutionId(executionId);
            if (history == null) {
                throw new RuntimeException("执行记录不存在");
            }

            Map<String, Object> metrics = new HashMap<>();
            metrics.put("executionId", executionId);
            metrics.put("taskId", history.getTaskId());
            metrics.put("taskName", history.getTaskName());
            metrics.put("executionStatus", history.getExecutionStatus());
            metrics.put("startTime", history.getStartTime());
            metrics.put("endTime", history.getEndTime());
            metrics.put("executionTime", history.getExecutionTime());
            metrics.put("processedRows", history.getProcessedRows());

            // 计算执行效率
            if (history.getExecutionTime() != null && history.getExecutionTime() > 0 &&
                history.getProcessedRows() != null && history.getProcessedRows() > 0) {
                double rowsPerSecond = (double) history.getProcessedRows() / (history.getExecutionTime() / 1000.0);
                metrics.put("rowsPerSecond", Math.round(rowsPerSecond * 100.0) / 100.0);
            }

            // 添加状态统计
            metrics.put("isSuccess", "SUCCESS".equals(history.getExecutionStatus()));
            metrics.put("isFailed", "FAILED".equals(history.getExecutionStatus()));
            metrics.put("isRunning", "RUNNING".equals(history.getExecutionStatus()));

            return metrics;

        } catch (Exception e) {
            log.error("获取执行指标失败，执行ID: {}", executionId, e);
            throw new RuntimeException("获取执行指标失败: " + e.getMessage());
        }
    }

    @Override
    public boolean validateEtlConfig(EtlTaskAddCommand command) {
        try {
            log.info("验证ETL配置，任务名称: {}", command.getTaskName());

            // 验证基本信息
            if (command.getTaskName() == null || command.getTaskName().trim().isEmpty()) {
                throw new RuntimeException("任务名称不能为空");
            }

            if (command.getSourceLayer() == null || command.getTargetLayer() == null) {
                throw new RuntimeException("源层级和目标层级不能为空");
            }

            if (command.getTargetTable() == null || command.getTargetTable().trim().isEmpty()) {
                throw new RuntimeException("目标表不能为空");
            }

            // 验证源表配置
            if (command.getSourceTablesConfig() == null || command.getSourceTablesConfig().trim().isEmpty()) {
                throw new RuntimeException("源表配置不能为空");
            }

            // 验证字段映射配置
            if (command.getFieldMappingsConfig() == null || command.getFieldMappingsConfig().trim().isEmpty()) {
                throw new RuntimeException("字段映射配置不能为空");
            }

            // 验证Cron表达式（如果是定时调度）
            if ("SCHEDULED".equals(command.getScheduleType()) &&
                (command.getCronExpression() == null || command.getCronExpression().trim().isEmpty())) {
                throw new RuntimeException("定时调度任务必须配置Cron表达式");
            }

            // 验证数据层级转换规则
            validateLayerTransition(command.getSourceLayer(), command.getTargetLayer());

            log.info("ETL配置验证通过");
            return true;

        } catch (Exception e) {
            log.error("ETL配置验证失败", e);
            throw new RuntimeException("ETL配置验证失败: " + e.getMessage());
        }
    }

    @Override
    public Object getDataLineage(Long taskId) {
        try {
            log.info("获取数据血缘关系，任务ID: {}", taskId);

            // 查询任务的上游和下游依赖
            List<Map<String, Object>> upstreamTasks = getUpstreamTasks(taskId);
            List<Map<String, Object>> downstreamTasks = getDownstreamTasks(taskId);

            // 构建血缘关系图
            Map<String, Object> lineage = new HashMap<>();
            lineage.put("taskId", taskId);
            lineage.put("upstreamTasks", upstreamTasks);
            lineage.put("downstreamTasks", downstreamTasks);
            lineage.put("lineageDepth", calculateLineageDepth(taskId));
            lineage.put("generateTime", LocalDateTime.now());

            return lineage;

        } catch (Exception e) {
            log.error("获取数据血缘关系失败，任务ID: {}", taskId, e);
            throw new RuntimeException("获取数据血缘关系失败: " + e.getMessage());
        }
    }

    @Override
    public Object getTaskDependencies(Long taskId) {
        try {
            log.info("获取任务依赖关系，任务ID: {}", taskId);

            Map<String, Object> dependencies = new HashMap<>();
            dependencies.put("taskId", taskId);
            dependencies.put("directDependencies", getDirectDependencies(taskId));
            dependencies.put("indirectDependencies", getIndirectDependencies(taskId));
            dependencies.put("dependentTasks", getDependentTasks(taskId));
            dependencies.put("generateTime", LocalDateTime.now());

            return dependencies;

        } catch (Exception e) {
            log.error("获取任务依赖关系失败，任务ID: {}", taskId, e);
            throw new RuntimeException("获取任务依赖关系失败: " + e.getMessage());
        }
    }

    /**
     * 创建执行历史记录
     */
    private EtlExecutionHistoryEntity createExecutionHistory(EtlTaskModel model, String executionId) {
        EtlExecutionHistoryEntity history = new EtlExecutionHistoryEntity();
        history.setExecutionId(executionId);
        history.setTaskId(model.getTaskId());
        history.setTaskName(model.getTaskName());
        history.setStatus("RUNNING");
        history.setExecutionStatus("RUNNING");
        history.setStartTime(new java.util.Date());
        history.setCreateTime(new java.util.Date());

        executionHistoryMapper.insert(history);
        return history;
    }

    /**
     * 执行ETL任务
     */
    private void executeEtlTask(EtlTaskModel model, EtlExecutionHistoryEntity history) {
        try {
            log.info("开始执行ETL任务，任务ID: {}", model.getTaskId());

            // 构建任务配置
            Map<String, Object> taskConfig = buildTaskConfig(model);

            // 生成ETL SQL
            List<String> sqlStatements = etlSqlGenerator.generateSql(taskConfig);

            // 使用Doris执行引擎执行SQL
            DorisExecutionEngine.ExecutionResult result = dorisExecutionEngine.executeStatements(sqlStatements);

            // 更新执行历史
            history.setStatus(result.getStatus());
            history.setExecutionStatus(result.getStatus());
            history.setEndTime(convertToDate(result.getEndTime()));
            history.setProcessedRecords(result.getProcessedRows());
            history.setProcessedRows(result.getProcessedRows());
            history.setDuration(result.getExecutionTimeMs());
            history.setExecutionTime(result.getExecutionTimeMs());

            if ("FAILED".equals(result.getStatus())) {
                history.setErrorMessage(result.getErrorMessage());
            }

            executionHistoryMapper.updateById(history);

            // 移除运行中标记
            runningTasks.remove(model.getTaskId());

            log.info("ETL任务执行完成，任务ID: {}, 状态: {}, 处理行数: {}, 耗时: {}ms",
                model.getTaskId(), result.getStatus(), result.getProcessedRows(), result.getExecutionTimeMs());

        } catch (Exception e) {
            log.error("ETL任务执行失败，任务ID: {}", model.getTaskId(), e);

            // 更新执行历史
            history.setStatus("FAILED");
            history.setExecutionStatus("FAILED");
            history.setEndTime(new java.util.Date());
            history.setErrorMessage(e.getMessage());
            executionHistoryMapper.updateById(history);

            // 移除运行中标记
            runningTasks.remove(model.getTaskId());
        }
    }

    /**
     * 构建任务配置
     */
    private Map<String, Object> buildTaskConfig(EtlTaskModel model) {
        Map<String, Object> config = new HashMap<>();
        config.put("taskId", model.getTaskId());
        config.put("taskName", model.getTaskName());
        config.put("sourceLayer", model.getSourceLayer());
        config.put("targetLayer", model.getTargetLayer());
        config.put("targetTable", model.getTargetTable());
        config.put("writeMode", model.getWriteMode());
        config.put("sourceTablesConfig", model.getSourceTablesConfig());
        config.put("fieldMappingsConfig", model.getFieldMappingsConfig());
        config.put("primaryKeys", model.getPrimaryKeys());
        return config;
    }

    /**
     * 生成预览SQL
     */
    private String generatePreviewSql(EtlTaskAddCommand command) {
        try {
            // 转换层级名称到数据库名称
            String sourceDatabase = convertLayerToDatabase(command.getSourceLayer());

            // 解析源表配置
            List<Map<String, Object>> sourceTables = objectMapper.readValue(
                command.getSourceTablesConfig(),
                objectMapper.getTypeFactory().constructCollectionType(List.class, Map.class)
            );

            if (sourceTables.isEmpty()) {
                throw new RuntimeException("源表配置不能为空");
            }

            // 解析字段映射配置
            List<Map<String, Object>> fieldMappings;
            if (command.getFieldMappingsConfig() != null && !command.getFieldMappingsConfig().trim().isEmpty()) {
                fieldMappings = objectMapper.readValue(
                    command.getFieldMappingsConfig(),
                    objectMapper.getTypeFactory().constructCollectionType(List.class, Map.class)
                );
            } else {
                // 如果没有字段映射配置，创建默认配置
                fieldMappings = new ArrayList<>();
                Map<String, Object> defaultMapping = new HashMap<>();
                defaultMapping.put("sourceField", "*");
                defaultMapping.put("targetField", "*");
                fieldMappings.add(defaultMapping);
            }

            // 构建SELECT语句
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("SELECT ");

            // 添加字段映射
            List<String> selectFields = new ArrayList<>();
            for (Map<String, Object> mapping : fieldMappings) {
                String sourceField = (String) mapping.get("sourceField");
                String targetField = (String) mapping.get("targetField");
                String expression = (String) mapping.get("expression");

                if (expression != null && !expression.trim().isEmpty()) {
                    selectFields.add(expression + " AS " + targetField);
                } else if ("*".equals(sourceField)) {
                    selectFields.add("*");
                } else {
                    selectFields.add(sourceField + " AS " + targetField);
                }
            }

            if (selectFields.isEmpty()) {
                selectFields.add("*");
            }

            sqlBuilder.append(String.join(", ", selectFields));

            // 添加FROM子句
            sqlBuilder.append(" FROM ");
            if (sourceTables.size() == 1) {
                Map<String, Object> sourceTable = sourceTables.get(0);
                sqlBuilder.append(sourceDatabase).append(".")
                    .append(sourceTable.get("tableName"));
            } else {
                // 多表关联（简化实现）
                for (int i = 0; i < sourceTables.size(); i++) {
                    Map<String, Object> sourceTable = sourceTables.get(i);
                    if (i > 0) {
                        sqlBuilder.append(" JOIN ");
                    }
                    sqlBuilder.append(sourceDatabase).append(".")
                        .append(sourceTable.get("tableName"));
                    if (i > 0) {
                        sqlBuilder.append(" ON 1=1"); // 简化的关联条件
                    }
                }
            }

            log.info("生成的预览SQL: {}", sqlBuilder.toString());
            return sqlBuilder.toString();

        } catch (Exception e) {
            log.error("生成预览SQL失败", e);
            throw new RuntimeException("生成预览SQL失败: " + e.getMessage());
        }
    }

    /**
     * 获取表字段信息
     */
    private List<Map<String, Object>> getTableFields(String layer, String tableName) {
        try {
            // 转换层级名称到实际数据库名称
            String database = convertLayerToDatabase(layer);

            log.info("获取表字段信息: {}.{}", database, tableName);

            // 先检查表是否存在
            if (!dorisExecutionEngine.tableExists(database, tableName)) {
                log.warn("表不存在: {}.{}", database, tableName);
                return new ArrayList<>();
            }

            List<Map<String, Object>> fields = dorisExecutionEngine.getTableFields(database, tableName);

            if (fields == null || fields.isEmpty()) {
                log.warn("表 {}.{} 没有字段信息", database, tableName);
                return new ArrayList<>();
            }

            // 转换字段信息格式
            List<Map<String, Object>> result = new ArrayList<>();
            for (Map<String, Object> field : fields) {
                Map<String, Object> fieldInfo = new HashMap<>();
                fieldInfo.put("fieldName", field.get("column_name"));
                fieldInfo.put("dataType", field.get("data_type"));
                fieldInfo.put("nullable", "YES".equals(field.get("is_nullable")));
                fieldInfo.put("defaultValue", field.get("column_default"));
                fieldInfo.put("comment", field.get("column_comment"));
                result.add(fieldInfo);

                log.debug("字段信息: {}", fieldInfo);
            }

            log.info("成功获取到{}个字段信息", result.size());
            return result;

        } catch (Exception e) {
            log.error("获取表字段信息失败: {}.{}", layer, tableName, e);
            return new ArrayList<>();
        }
    }

    /**
     * 转换层级名称到数据库名称
     */
    private String convertLayerToDatabase(String layer) {
        switch (layer.toUpperCase()) {
            case "ODS":
                return "ods_db";
            case "DWD":
                return "dwd_db";
            case "DWS":
                return "dws_db";
            case "ADS":
                return "ads_db";
            default:
                return layer.toLowerCase() + "_db";
        }
    }

    /**
     * 生成目标表名建议
     */
    private String generateTargetTableName(String sourceTable, String sourceLayer, String targetLayer) {
        // 根据数据仓库分层规则生成目标表名
        switch (targetLayer.toLowerCase()) {
            case "dwd":
                return "dwd_" + sourceTable.replaceFirst("^ods_", "");
            case "dws":
                return "dws_" + sourceTable.replaceFirst("^(ods_|dwd_)", "");
            case "ads":
                return "ads_" + sourceTable.replaceFirst("^(ods_|dwd_|dws_)", "");
            default:
                return targetLayer.toLowerCase() + "_" + sourceTable;
        }
    }

    /**
     * 生成目标字段建议
     */
    private List<Map<String, Object>> generateTargetFields(List<Map<String, Object>> sourceFields, String targetLayer) {
        List<Map<String, Object>> targetFields = new ArrayList<>();

        for (Map<String, Object> sourceField : sourceFields) {
            Map<String, Object> targetField = new HashMap<>(sourceField);

            // 根据目标层级调整字段类型和属性
            String dataType = (String) sourceField.get("dataType");
            switch (targetLayer.toLowerCase()) {
                case "dwd":
                    // DWD层保持原始数据类型，但可能需要清洗
                    break;
                case "dws":
                    // DWS层可能需要聚合，调整数据类型
                    if ("varchar".equalsIgnoreCase(dataType)) {
                        targetField.put("dataType", "string");
                    }
                    break;
                case "ads":
                    // ADS层面向应用，可能需要特殊处理
                    break;
            }

            targetFields.add(targetField);
        }

        // 添加通用字段
        addCommonFields(targetFields, targetLayer);

        return targetFields;
    }

    /**
     * 添加通用字段
     */
    private void addCommonFields(List<Map<String, Object>> fields, String targetLayer) {
        // 添加ETL处理时间字段
        Map<String, Object> etlTimeField = new HashMap<>();
        etlTimeField.put("fieldName", "etl_time");
        etlTimeField.put("dataType", "datetime");
        etlTimeField.put("nullable", false);
        etlTimeField.put("comment", "ETL处理时间");
        fields.add(etlTimeField);

        // 添加数据版本字段
        Map<String, Object> versionField = new HashMap<>();
        versionField.put("fieldName", "data_version");
        versionField.put("dataType", "bigint");
        versionField.put("nullable", false);
        versionField.put("comment", "数据版本号");
        fields.add(versionField);
    }

    /**
     * 生成字段映射建议
     */
    private List<Map<String, Object>> generateFieldMappings(List<Map<String, Object>> sourceFields,
                                                           List<Map<String, Object>> targetFields) {
        List<Map<String, Object>> mappings = new ArrayList<>();

        for (Map<String, Object> sourceField : sourceFields) {
            String sourceFieldName = (String) sourceField.get("fieldName");

            // 查找匹配的目标字段
            Map<String, Object> matchedTargetField = findMatchingTargetField(sourceFieldName, targetFields);

            if (matchedTargetField != null) {
                Map<String, Object> mapping = new HashMap<>();
                mapping.put("sourceField", sourceFieldName);
                mapping.put("targetField", matchedTargetField.get("fieldName"));
                mapping.put("sourceDataType", sourceField.get("dataType"));
                mapping.put("targetDataType", matchedTargetField.get("dataType"));
                mapping.put("mappingType", "DIRECT");
                mapping.put("expression", "");
                mappings.add(mapping);
            }
        }

        return mappings;
    }

    /**
     * 查找匹配的目标字段
     */
    private Map<String, Object> findMatchingTargetField(String sourceFieldName, List<Map<String, Object>> targetFields) {
        // 精确匹配
        for (Map<String, Object> targetField : targetFields) {
            if (sourceFieldName.equals(targetField.get("fieldName"))) {
                return targetField;
            }
        }

        // 模糊匹配
        for (Map<String, Object> targetField : targetFields) {
            String targetFieldName = (String) targetField.get("fieldName");
            if (sourceFieldName.toLowerCase().contains(targetFieldName.toLowerCase()) ||
                targetFieldName.toLowerCase().contains(sourceFieldName.toLowerCase())) {
                return targetField;
            }
        }

        return null;
    }

    /**
     * 验证数据层级转换规则
     */
    private void validateLayerTransition(String sourceLayer, String targetLayer) {
        // 定义允许的层级转换规则
        Map<String, List<String>> allowedTransitions = new HashMap<>();
        allowedTransitions.put("ods", Arrays.asList("dwd", "dws", "ads"));
        allowedTransitions.put("dwd", Arrays.asList("dws", "ads"));
        allowedTransitions.put("dws", Arrays.asList("ads"));

        List<String> allowedTargets = allowedTransitions.get(sourceLayer.toLowerCase());
        if (allowedTargets == null || !allowedTargets.contains(targetLayer.toLowerCase())) {
            throw new RuntimeException("不支持的数据层级转换: " + sourceLayer + " -> " + targetLayer);
        }
    }



    /**
     * 获取上游任务
     */
    private List<Map<String, Object>> getUpstreamTasks(Long taskId) {
        // 查询依赖当前任务的上游任务
        List<Map<String, Object>> upstreamTasks = new ArrayList<>();

        // 这里应该查询任务依赖关系表
        // 简化实现，返回空列表

        return upstreamTasks;
    }

    /**
     * 获取下游任务
     */
    private List<Map<String, Object>> getDownstreamTasks(Long taskId) {
        // 查询当前任务的下游任务
        List<Map<String, Object>> downstreamTasks = new ArrayList<>();

        // 这里应该查询任务依赖关系表
        // 简化实现，返回空列表

        return downstreamTasks;
    }

    /**
     * 计算血缘深度
     */
    private int calculateLineageDepth(Long taskId) {
        // 计算数据血缘的最大深度
        return 3; // 简化实现
    }

    /**
     * 获取直接依赖
     */
    private List<Map<String, Object>> getDirectDependencies(Long taskId) {
        List<Map<String, Object>> dependencies = new ArrayList<>();

        // 查询直接依赖的任务
        // 简化实现

        return dependencies;
    }

    /**
     * 获取间接依赖
     */
    private List<Map<String, Object>> getIndirectDependencies(Long taskId) {
        List<Map<String, Object>> dependencies = new ArrayList<>();

        // 查询间接依赖的任务
        // 简化实现

        return dependencies;
    }

    /**
     * 获取依赖当前任务的任务
     */
    private List<Map<String, Object>> getDependentTasks(Long taskId) {
        List<Map<String, Object>> dependentTasks = new ArrayList<>();

        // 查询依赖当前任务的其他任务
        // 简化实现

        return dependentTasks;
    }

    /**
     * 转换LocalDateTime到Date
     */
    private java.util.Date convertToDate(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return java.util.Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }
}
