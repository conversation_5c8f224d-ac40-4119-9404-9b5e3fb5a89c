import request from '@/utils/request'

// 查询ETL任务列表
export function listEtlTask(query) {
  return request({
    url: '/datawarehouse/etl/list',
    method: 'get',
    params: query
  })
}

// 查询ETL任务详细
export function getEtlTask(taskId) {
  return request({
    url: '/datawarehouse/etl/' + taskId,
    method: 'get'
  })
}

// 新增ETL任务
export function addEtlTask(data) {
  return request({
    url: '/datawarehouse/etl',
    method: 'post',
    data: data
  })
}

// 修改ETL任务
export function updateEtlTask(data) {
  return request({
    url: '/datawarehouse/etl',
    method: 'put',
    data: data
  })
}

// 删除ETL任务
export function deleteEtlTask(taskId) {
  return request({
    url: '/datawarehouse/etl/' + taskId,
    method: 'delete'
  })
}

// 更新ETL任务状态
export function updateEtlTaskStatus(taskId, status) {
  return request({
    url: '/datawarehouse/etl/status',
    method: 'put',
    data: {
      taskId,
      status
    }
  })
}

// 执行ETL任务
export function runEtlTask(taskId) {
  return request({
    url: '/datawarehouse/etl/run/' + taskId,
    method: 'post'
  })
}

// 提交ETL任务执行（新方法）
export function submitTask(taskModel) {
  return request({
    url: '/datawarehouse/etl/execution/submit',
    method: 'post',
    data: taskModel
  })
}

// 停止ETL任务
export function stopEtlTask(taskId) {
  return request({
    url: '/datawarehouse/etl/stop/' + taskId,
    method: 'post'
  })
}

// 停止ETL任务执行（新方法）
export function stopTask(taskId) {
  return request({
    url: '/datawarehouse/etl/execution/stop',
    method: 'post',
    data: { taskId }
  })
}

// 检查任务运行状态
export function isTaskRunning(taskId) {
  return request({
    url: '/datawarehouse/etl/execution/running/' + taskId,
    method: 'get'
  })
}

// 获取执行历史
export function getExecutionHistory(taskId) {
  return request({
    url: '/datawarehouse/etl/history/' + taskId,
    method: 'get'
  })
}

// 获取执行日志
export function getExecutionLog(executionId) {
  return request({
    url: '/datawarehouse/etl/log/' + executionId,
    method: 'get'
  })
}

// 获取执行指标
export function getExecutionMetrics(executionId) {
  return request({
    url: '/datawarehouse/etl/metrics/' + executionId,
    method: 'get'
  })
}

// 验证ETL配置
export function validateEtlConfig(data) {
  return request({
    url: '/datawarehouse/etl/validate',
    method: 'post',
    data: data
  })
}

// 预览ETL结果
export function previewEtlResult(data) {
  return request({
    url: '/datawarehouse/etl/preview',
    method: 'post',
    data: data
  })
}

// 获取字段映射建议
export function getFieldMappingSuggestions(sourceLayer, targetLayer, sourceTables) {
  return request({
    url: '/datawarehouse/etl/mapping-suggestions',
    method: 'post',
    data: {
      sourceLayer,
      targetLayer,
      sourceTables
    }
  })
}

// 获取数据血缘关系
export function getDataLineage(taskId) {
  return request({
    url: '/datawarehouse/etl/lineage/' + taskId,
    method: 'get'
  })
}

// 获取任务依赖关系
export function getTaskDependencies(taskId) {
  return request({
    url: '/datawarehouse/etl/dependencies/' + taskId,
    method: 'get'
  })
}

// 批量执行ETL任务
export function batchRunEtlTasks(taskIds) {
  return request({
    url: '/datawarehouse/etl/batch-run',
    method: 'post',
    data: {
      taskIds
    }
  })
}

// 获取ETL任务统计信息
export function getEtlTaskStats() {
  return request({
    url: '/datawarehouse/etl/stats',
    method: 'get'
  })
}

// 导出ETL任务配置
export function exportEtlTaskConfig(taskId) {
  return request({
    url: '/datawarehouse/etl/export/' + taskId,
    method: 'get',
    responseType: 'blob'
  })
}

// 导入ETL任务配置
export function importEtlTaskConfig(data) {
  return request({
    url: '/datawarehouse/etl/import',
    method: 'post',
    data: data
  })
}
