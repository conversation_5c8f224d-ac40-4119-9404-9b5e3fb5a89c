package com.lacus.web.controller.datawarehouse;

import com.lacus.common.core.domain.R;
import com.lacus.service.datawarehouse.util.DorisConnectionTest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Doris测试控制器
 * 用于测试Doris连接和调试数据仓库功能
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/datawarehouse/test")
@Slf4j
public class DorisTestController {

    @Autowired
    private DorisConnectionTest dorisConnectionTest;

    @Autowired
    @Qualifier("dorisJdbcTemplate")
    private JdbcTemplate dorisJdbcTemplate;

    /**
     * 测试Doris连接
     */
    @GetMapping("/connection")
    public R<Map<String, Object>> testConnection() {
        try {
            Map<String, Object> result = new HashMap<>();
            
            // 测试连接
            boolean connected = dorisConnectionTest.testDorisConnection();
            result.put("connected", connected);
            
            if (connected) {
                // 获取数据库列表
                List<Map<String, Object>> databases = dorisJdbcTemplate.queryForList("SHOW DATABASES");
                result.put("databases", databases);
                
                // 检查数据仓库数据库
                Map<String, Boolean> warehouseDbs = new HashMap<>();
                warehouseDbs.put("ods_db", checkDatabaseExists("ods_db"));
                warehouseDbs.put("dwd_db", checkDatabaseExists("dwd_db"));
                warehouseDbs.put("dws_db", checkDatabaseExists("dws_db"));
                warehouseDbs.put("ads_db", checkDatabaseExists("ads_db"));
                result.put("warehouseDatabases", warehouseDbs);
            }
            
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("测试Doris连接失败", e);
            return R.fail("测试连接失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定数据库的表列表
     */
    @GetMapping("/tables/{database}")
    public R<List<Map<String, Object>>> getTables(@PathVariable String database) {
        try {
            String sql = "SELECT table_name, table_comment FROM information_schema.tables WHERE table_schema = ? ORDER BY table_name";
            List<Map<String, Object>> tables = dorisJdbcTemplate.queryForList(sql, database);
            return R.ok(tables);
        } catch (Exception e) {
            log.error("获取表列表失败: {}", database, e);
            return R.fail("获取表列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定表的字段信息
     */
    @GetMapping("/fields/{database}/{tableName}")
    public R<List<Map<String, Object>>> getTableFields(@PathVariable String database, @PathVariable String tableName) {
        try {
            List<Map<String, Object>> fields = dorisConnectionTest.getTableFields(database, tableName);
            return R.ok(fields);
        } catch (Exception e) {
            log.error("获取表字段失败: {}.{}", database, tableName, e);
            return R.fail("获取表字段失败: " + e.getMessage());
        }
    }

    /**
     * 创建测试数据库和表
     */
    @PostMapping("/create-test-data")
    public R<String> createTestData() {
        try {
            dorisConnectionTest.createTestTableIfNotExists();
            return R.ok("测试数据创建成功");
        } catch (Exception e) {
            log.error("创建测试数据失败", e);
            return R.fail("创建测试数据失败: " + e.getMessage());
        }
    }

    /**
     * 执行测试SQL
     */
    @PostMapping("/execute-sql")
    public R<Object> executeSql(@RequestBody Map<String, String> request) {
        try {
            String sql = request.get("sql");
            if (sql == null || sql.trim().isEmpty()) {
                return R.fail("SQL语句不能为空");
            }

            // 安全检查：只允许SELECT语句
            if (!sql.trim().toUpperCase().startsWith("SELECT")) {
                return R.fail("只允许执行SELECT查询语句");
            }

            List<Map<String, Object>> result = dorisJdbcTemplate.queryForList(sql);
            
            Map<String, Object> response = new HashMap<>();
            response.put("data", result);
            response.put("rowCount", result.size());
            response.put("sql", sql);
            
            return R.ok(response);
            
        } catch (Exception e) {
            log.error("执行SQL失败", e);
            return R.fail("执行SQL失败: " + e.getMessage());
        }
    }

    /**
     * 获取表数据预览
     */
    @GetMapping("/preview/{database}/{tableName}")
    public R<Object> previewTable(@PathVariable String database, @PathVariable String tableName,
                                  @RequestParam(defaultValue = "10") int limit) {
        try {
            // 限制最大预览行数
            if (limit > 100) {
                limit = 100;
            }
            
            String sql = String.format("SELECT * FROM %s.%s LIMIT %d", database, tableName, limit);
            List<Map<String, Object>> data = dorisJdbcTemplate.queryForList(sql);
            
            Map<String, Object> result = new HashMap<>();
            result.put("data", data);
            result.put("rowCount", data.size());
            result.put("columns", data.isEmpty() ? 0 : data.get(0).size());
            result.put("sql", sql);
            
            return R.ok(result);
            
        } catch (Exception e) {
            log.error("预览表数据失败: {}.{}", database, tableName, e);
            return R.fail("预览表数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取数据库统计信息
     */
    @GetMapping("/stats/{database}")
    public R<Map<String, Object>> getDatabaseStats(@PathVariable String database) {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // 表数量
            String tableCountSql = "SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = ?";
            Integer tableCount = dorisJdbcTemplate.queryForObject(tableCountSql, Integer.class, database);
            stats.put("tableCount", tableCount);
            
            // 获取每个表的行数（前10个表）
            String tablesSql = "SELECT table_name FROM information_schema.tables WHERE table_schema = ? LIMIT 10";
            List<Map<String, Object>> tables = dorisJdbcTemplate.queryForList(tablesSql, database);
            
            Map<String, Long> tableRows = new HashMap<>();
            for (Map<String, Object> table : tables) {
                String tableName = (String) table.get("table_name");
                try {
                    String rowCountSql = String.format("SELECT COUNT(*) FROM %s.%s", database, tableName);
                    Long rowCount = dorisJdbcTemplate.queryForObject(rowCountSql, Long.class);
                    tableRows.put(tableName, rowCount);
                } catch (Exception e) {
                    tableRows.put(tableName, -1L); // 表示查询失败
                }
            }
            stats.put("tableRows", tableRows);
            
            return R.ok(stats);
            
        } catch (Exception e) {
            log.error("获取数据库统计信息失败: {}", database, e);
            return R.fail("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 检查数据库是否存在
     */
    private boolean checkDatabaseExists(String database) {
        try {
            String sql = "SELECT COUNT(*) FROM information_schema.schemata WHERE schema_name = ?";
            Integer count = dorisJdbcTemplate.queryForObject(sql, Integer.class, database);
            return count != null && count > 0;
        } catch (Exception e) {
            return false;
        }
    }
}
