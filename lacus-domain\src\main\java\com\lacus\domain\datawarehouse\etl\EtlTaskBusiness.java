package com.lacus.domain.datawarehouse.etl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lacus.common.core.page.PageDTO;
import com.lacus.service.datawarehouse.command.EtlTaskAddCommand;
import com.lacus.service.datawarehouse.command.EtlTaskUpdateCommand;
import com.lacus.service.datawarehouse.dto.EtlTaskDTO;
import com.lacus.service.datawarehouse.dto.EtlTaskStatsDTO;
import com.lacus.service.datawarehouse.model.EtlTaskModel;
import com.lacus.service.datawarehouse.query.EtlExecutionHistoryQuery;
import com.lacus.service.datawarehouse.query.EtlTaskQuery;
import com.lacus.service.datawarehouse.EtlExecutionService;
import com.lacus.service.datawarehouse.EtlTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;

/**
 * ETL任务业务逻辑
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EtlTaskBusiness {

    @Autowired
    private EtlTaskService etlTaskService;

    @Autowired
    private  EtlExecutionService etlExecutionService;

    /**
     * 查询ETL任务列表
     */
    public PageDTO queryEtlTaskList(@Valid EtlTaskQuery query) {
        Page<?> page = etlTaskService.page(query.toPage(), query.toQueryWrapper());
        return new PageDTO(page.getRecords(), page.getTotal());
    }

    /**
     * 根据ID查询ETL任务
     */
    public EtlTaskDTO queryEtlTaskById(Long taskId) {
        return etlTaskService.queryEtlTaskDtoById(taskId);
    }

    /**
     * 新增ETL任务
     */
    @Transactional(rollbackFor = Exception.class)
    public Long addEtlTask(EtlTaskAddCommand command) {
        // 检查任务名称是否重复
        if (etlTaskService.checkTaskNameExists(command.getTaskName(), null)) {
            throw new RuntimeException("任务名称已存在");
        }

        // 验证配置
        validateEtlTaskConfig(command);

        // 创建任务模型
        EtlTaskModel model = EtlTaskModel.create(command);

        // 保存任务
        Long taskId = etlTaskService.saveEtlTask(model);

        log.info("新增ETL任务成功，任务ID: {}, 任务名称: {}", taskId, command.getTaskName());
        return taskId;
    }

    /**
     * 更新ETL任务
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateEtlTask(EtlTaskUpdateCommand command) {
        // 检查任务是否存在
        EtlTaskModel model = etlTaskService.queryEtlTaskById(command.getTaskId());
        if (model == null) {
            throw new RuntimeException("ETL任务不存在");
        }
        // 检查任务名称是否重复
        if (etlTaskService.checkTaskNameExists(command.getTaskName(), command.getTaskId())) {
            throw new RuntimeException("任务名称已存在");
        }

        // 验证配置
        validateEtlTaskConfig(command);

        // 更新任务
        model.update(command);
        etlTaskService.updateEtlTask(model);

        log.info("更新ETL任务成功，任务ID: {}, 任务名称: {}", command.getTaskId(), command.getTaskName());
    }

    /**
     * 删除ETL任务
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteEtlTask(Long taskId) {
        EtlTaskModel model = etlTaskService.queryEtlTaskById(taskId);
        if (model == null) {
            throw new RuntimeException("ETL任务不存在");
        }

        // 检查任务是否正在运行
        if (etlExecutionService.isTaskRunning(taskId)) {
            throw new RuntimeException("任务正在运行中，无法删除");
        }

        etlTaskService.deleteEtlTask(taskId);
        log.info("删除ETL任务成功，任务ID: {}", taskId);
    }

    /**
     * 批量删除ETL任务
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteEtlTask(List<Long> taskIds) {
        for (Long taskId : taskIds) {
            deleteEtlTask(taskId);
        }
    }

    /**
     * 更新任务状态
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateEtlTaskStatus(Long taskId, Integer status) {
        EtlTaskModel model = etlTaskService.queryEtlTaskById(taskId);
        if (model == null) {
            throw new RuntimeException("ETL任务不存在");
        }

        model.updateStatus(status);
        etlTaskService.updateEtlTask(model);

        log.info("更新ETL任务状态成功，任务ID: {}, 状态: {}", taskId, status);
    }

    /**
     * 执行ETL任务
     */
    @Transactional(rollbackFor = Exception.class)
    public String runEtlTask(Long taskId) {
        EtlTaskModel model = etlTaskService.queryEtlTaskById(taskId);
        if (model == null) {
            throw new RuntimeException("ETL任务不存在");
        }

        if (!model.canExecute()) {
            throw new RuntimeException("任务状态不允许执行");
        }

        // 检查任务是否已在运行
        if (etlExecutionService.isTaskRunning(taskId)) {
            throw new RuntimeException("任务正在运行中");
        }

        // 提交执行
        String executionId = etlExecutionService.submitTask(model);

        // 更新最后执行时间
        model.updateExecutionInfo(LocalDateTime.now(), "RUNNING");
        etlTaskService.updateEtlTask(model);

        log.info("提交ETL任务执行成功，任务ID: {}, 执行ID: {}", taskId, executionId);
        return executionId;
    }

    /**
     * 停止ETL任务
     */
    @Transactional(rollbackFor = Exception.class)
    public void stopEtlTask(Long taskId) {
        EtlTaskModel model = etlTaskService.queryEtlTaskById(taskId);
        if (model == null) {
            throw new RuntimeException("ETL任务不存在");
        }

        etlExecutionService.stopTask(taskId);
        log.info("停止ETL任务成功，任务ID: {}", taskId);
    }

    /**
     * 获取执行历史
     */
    public PageDTO getExecutionHistory(EtlExecutionHistoryQuery pageQuery) {
        Page<?> page = etlExecutionService.page(pageQuery.toPage(), pageQuery.toQueryWrapper());
        return new PageDTO(page.getRecords(), page.getTotal());
    }

    /**
     * 获取ETL任务统计信息
     */
    public EtlTaskStatsDTO getEtlTaskStats() {
        return etlTaskService.getEtlTaskStats();
    }

    /**
     * 验证ETL任务配置
     */
    private void validateEtlTaskConfig(Object command) {
        // 这里可以添加具体的配置验证逻辑
        // 例如：验证Cron表达式、验证字段映射配置等
        log.debug("验证ETL任务配置");
    }

    /**
     * 预览ETL结果
     */
    public Object previewEtlResult(EtlTaskAddCommand command) {
        // 验证配置
        validateEtlTaskConfig(command);

        // 执行预览逻辑
        return etlExecutionService.previewResult(command);
    }

    /**
     * 获取字段映射建议
     */
    public Object getFieldMappingSuggestions(String sourceLayer, String targetLayer, List<String> sourceTables) {
        return etlExecutionService.getFieldMappingSuggestions(sourceLayer, targetLayer, sourceTables);
    }

    /**
     * 根据目标表生成智能字段映射
     */
    public Object generateIntelligentMapping(String sourceLayer, String targetLayer, List<String> sourceTables, String targetTable) {
        return etlExecutionService.generateFieldMappingsByTargetTable(sourceLayer, targetLayer, sourceTables, targetTable);
    }
}
