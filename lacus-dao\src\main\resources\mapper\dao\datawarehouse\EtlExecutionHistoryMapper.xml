<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lacus.dao.datawarehouse.mapper.EtlExecutionHistoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.lacus.dao.datawarehouse.entity.EtlExecutionHistoryEntity">
        <id column="execution_id" property="executionId" />
        <result column="task_id" property="taskId" />
        <result column="task_name" property="taskName" />
        <result column="status" property="status" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="duration" property="duration" />
        <result column="processed_records" property="processedRecords" />
        <result column="error_records" property="errorRecords" />
        <result column="error_message" property="errorMessage" />
        <result column="log_path" property="logPath" />
        <result column="execution_params" property="executionParams" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="selectExecutionHistoryVo">
        select execution_id, task_id, task_name, status, start_time, end_time, duration,
               processed_records, error_records, error_message, log_path, execution_params, create_time
        from etl_execution_history
    </sql>

    <!-- 分页查询执行历史 -->
    <select id="selectExecutionHistoryPage" resultMap="BaseResultMap">
        <include refid="selectExecutionHistoryVo"/>
        <where>
            <if test="params.taskId != null">
                task_id = #{params.taskId}
            </if>
            <if test="params.status != null and params.status != ''">
                AND status = #{params.status}
            </if>
            <if test="params.taskName != null and params.taskName != ''">
                AND task_name like concat('%', #{params.taskName}, '%')
            </if>
            <if test="params.beginTime != null and params.beginTime != ''">
                AND start_time >= #{params.beginTime}
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                AND start_time &lt;= #{params.endTime}
            </if>
        </where>
        order by start_time desc
    </select>

    <!-- 根据执行ID查询执行记录 -->
    <select id="selectByExecutionId" resultMap="BaseResultMap">
        <include refid="selectExecutionHistoryVo"/>
        where execution_id = #{executionId}
    </select>

    <!-- 根据任务ID查询执行历史 -->
    <select id="selectByTaskId" resultMap="BaseResultMap">
        <include refid="selectExecutionHistoryVo"/>
        where task_id = #{taskId}
        order by start_time desc
    </select>

    <!-- 根据执行状态查询历史 -->
    <select id="selectByStatus" resultMap="BaseResultMap">
        <include refid="selectExecutionHistoryVo"/>
        where status = #{status}
        order by start_time desc
    </select>

    <!-- 获取任务最近的执行记录 -->
    <select id="selectLatestByTaskId" resultMap="BaseResultMap">
        <include refid="selectExecutionHistoryVo"/>
        where task_id = #{taskId}
        order by start_time desc
        limit 1
    </select>

    <!-- 获取执行统计信息 -->
    <select id="selectExecutionStats" resultType="map">
        select
            count(*) as totalExecutions,
            sum(case when status = 'SUCCESS' then 1 else 0 end) as successExecutions,
            sum(case when status = 'FAILED' then 1 else 0 end) as failedExecutions,
            sum(case when status = 'RUNNING' then 1 else 0 end) as runningExecutions,
            avg(duration) as avgDuration,
            sum(processed_records) as totalProcessedRecords,
            sum(error_records) as totalErrorRecords
        from etl_execution_history
        <where>
            <if test="taskId != null">
                task_id = #{taskId}
            </if>
        </where>
    </select>

    <!-- 获取今日执行统计 -->
    <select id="selectTodayStats" resultType="map">
        select
            count(*) as todayExecutions,
            sum(case when status = 'SUCCESS' then 1 else 0 end) as todaySuccessExecutions,
            sum(case when status = 'FAILED' then 1 else 0 end) as todayFailedExecutions,
            sum(case when status = 'RUNNING' then 1 else 0 end) as todayRunningExecutions
        from etl_execution_history
        where date(start_time) = curdate()
    </select>

    <!-- 清理历史数据 -->
    <delete id="deleteOldRecords">
        delete from etl_execution_history
        where start_time &lt; date_sub(now(), interval #{days} day)
    </delete>

    <!-- 获取执行趋势数据 -->
    <select id="selectExecutionTrend" resultType="map">
        select
            date(start_time) as date,
            count(*) as totalExecutions,
            sum(case when status = 'SUCCESS' then 1 else 0 end) as successExecutions,
            sum(case when status = 'FAILED' then 1 else 0 end) as failedExecutions,
            avg(duration) as avgDuration
        from etl_execution_history
        where start_time >= date_sub(curdate(), interval #{days} day)
        <if test="taskId != null">
            and task_id = #{taskId}
        </if>
        group by date(start_time)
        order by date(start_time)
    </select>

</mapper>
