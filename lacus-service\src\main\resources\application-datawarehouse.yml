# 数据仓库配置文件
datawarehouse:
  # ETL配置
  etl:
    # 最大并发任务数
    max-concurrent-tasks: 10
    # 任务超时时间（分钟）
    task-timeout-minutes: 60
    # 最大重试次数
    max-retry-count: 3
    # 执行历史保留天数
    history-retention-days: 30
    # 预览数据最大行数
    preview-max-rows: 100
    # 支持的数据层级
    supported-layers:
      - ods_db
      - dwd_db
      - dws_db
      - ads_db
    # 层级转换规则
    layer-transitions:
      ods_db:
        - dwd_db
        - dws_db
        - ads_db
      dwd_db:
        - dws_db
        - ads_db
      dws_db:
        - ads_db
    # 写入模式配置
    write-modes:
      - APPEND
      - OVERWRITE
      - UPSERT
    # 默认字段配置
    default-fields:
      etl-time:
        name: etl_time
        type: DATETIME
        comment: ETL处理时间
        default: NOW()
      data-version:
        name: data_version
        type: BIGINT
        comment: 数据版本号
        default: UNIX_TIMESTAMP()

  # 质量监控配置
  quality:
    # 默认检查频率
    default-check-frequency: DAILY
    # 质量告警阈值
    alert-threshold: 80
    # 质量评分权重配置
    score-weights:
      NULL_CHECK: 3
      DUPLICATE_CHECK: 2
      RANGE_CHECK: 2
      FORMAT_CHECK: 1
      CUSTOM_SQL: 2
    # 质量等级配置
    quality-levels:
      EXCELLENT: 90
      GOOD: 80
      FAIR: 60
      POOR: 0
    # 支持的规则类型
    supported-rule-types:
      - NULL_CHECK
      - DUPLICATE_CHECK
      - RANGE_CHECK
      - FORMAT_CHECK
      - CUSTOM_SQL
      - RECORD_COUNT_CHECK
    # 质量检查历史保留天数
    check-history-retention-days: 90

  # 调度配置
  schedule:
    # 最大并发调度任务数
    max-concurrent-jobs: 5
    # 调度器线程池大小
    scheduler-pool-size: 10
    # 任务执行超时时间（分钟）
    job-timeout-minutes: 120
    # 失败重试间隔（分钟）
    retry-interval-minutes: 5
    # 最大重试次数
    max-retry-count: 3
    # 调度历史保留天数
    history-retention-days: 60
    # 支持的调度类型
    supported-schedule-types:
      - MANUAL
      - CRON
      - REALTIME
    # 默认时区
    default-timezone: Asia/Shanghai
    # Cron表达式模板
    cron-templates:
      - name: 每分钟执行
        expression: "0 * * * * ?"
        description: 每分钟执行一次
      - name: 每5分钟执行
        expression: "0 */5 * * * ?"
        description: 每5分钟执行一次
      - name: 每小时执行
        expression: "0 0 * * * ?"
        description: 每小时执行一次
      - name: 每日凌晨2点执行
        expression: "0 0 2 * * ?"
        description: 每日凌晨2点执行
      - name: 每周一凌晨2点执行
        expression: "0 0 2 ? * MON"
        description: 每周一凌晨2点执行

  # Doris数据库配置
  doris:
    # 连接超时时间（秒）
    connection-timeout: 30
    # 查询超时时间（秒）
    query-timeout: 300
    # 最大连接数
    max-connections: 50
    # 最小空闲连接数
    min-idle: 5
    # 连接池配置
    pool:
      initial-size: 5
      max-active: 20
      max-wait: 60000
      validation-query: SELECT 1
      test-on-borrow: true
      test-while-idle: true
    # 数据库映射
    databases:
      ods: ods_db
      dwd: dwd_db
      dws: dws_db
      ads: ads_db

  # 线程池配置
  thread-pool:
    # ETL执行器配置
    etl-executor:
      core-pool-size: 5
      max-pool-size: 20
      queue-capacity: 100
      thread-name-prefix: etl-executor-
      keep-alive-seconds: 60
    # 质量监控执行器配置
    quality-executor:
      core-pool-size: 3
      max-pool-size: 10
      queue-capacity: 50
      thread-name-prefix: quality-executor-
      keep-alive-seconds: 60
    # 调度任务执行器配置
    schedule-executor:
      core-pool-size: 2
      max-pool-size: 8
      queue-capacity: 30
      thread-name-prefix: schedule-executor-
      keep-alive-seconds: 60

  # 监控配置
  monitoring:
    # 是否启用监控
    enabled: true
    # 监控数据收集间隔（秒）
    collect-interval: 30
    # 监控数据保留天数
    retention-days: 7
    # 性能指标配置
    metrics:
      # 是否收集JVM指标
      jvm-enabled: true
      # 是否收集数据库指标
      database-enabled: true
      # 是否收集任务执行指标
      task-enabled: true

  # 告警配置
  alert:
    # 是否启用告警
    enabled: true
    # 告警检查间隔（分钟）
    check-interval: 5
    # 告警规则配置
    rules:
      # ETL任务失败告警
      etl-task-failed:
        enabled: true
        threshold: 1
        message: "ETL任务执行失败"
      # 质量检查失败告警
      quality-check-failed:
        enabled: true
        threshold: 80
        message: "数据质量检查未通过"
      # 调度任务超时告警
      schedule-timeout:
        enabled: true
        threshold: 120
        message: "调度任务执行超时"
    # 告警通知配置
    notification:
      # 邮件通知
      email:
        enabled: false
        smtp-host: smtp.example.com
        smtp-port: 587
        username: <EMAIL>
        password: password
        from: <EMAIL>
        to:
          - <EMAIL>
      # 钉钉通知
      dingtalk:
        enabled: false
        webhook-url: https://oapi.dingtalk.com/robot/send?access_token=xxx
      # 企业微信通知
      wechat:
        enabled: false
        webhook-url: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=xxx

  # 安全配置
  security:
    # 是否启用权限检查
    permission-check-enabled: true
    # 操作日志记录
    operation-log:
      enabled: true
      retention-days: 90
    # 数据脱敏配置
    data-masking:
      enabled: false
      rules:
        - field-pattern: ".*phone.*"
          mask-type: PHONE
        - field-pattern: ".*email.*"
          mask-type: EMAIL
        - field-pattern: ".*id_card.*"
          mask-type: ID_CARD

  # 缓存配置
  cache:
    # 是否启用缓存
    enabled: true
    # 缓存类型：redis, caffeine
    type: caffeine
    # 缓存配置
    configs:
      # 任务配置缓存
      task-config:
        expire-after-write: 300
        maximum-size: 1000
      # 表结构缓存
      table-schema:
        expire-after-write: 600
        maximum-size: 500
      # 执行结果缓存
      execution-result:
        expire-after-write: 60
        maximum-size: 100

  # 文件存储配置
  file-storage:
    # 存储类型：local, oss, s3
    type: local
    # 本地存储配置
    local:
      base-path: /data/datawarehouse
      temp-path: /data/datawarehouse/temp
    # 文件清理配置
    cleanup:
      enabled: true
      # 临时文件保留时间（小时）
      temp-file-retention-hours: 24
      # 日志文件保留天数
      log-file-retention-days: 30

# 日志配置
logging:
  level:
    com.lacus.service.datawarehouse: INFO
    com.lacus.dao.datawarehouse: DEBUG
  pattern:
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{50} - %msg%n"
  file:
    name: logs/datawarehouse.log
    max-size: 100MB
    max-history: 30
