package com.lacus.service.datawarehouse.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.regex.Pattern;

/**
 * Cron表达式工具类
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class CronExpressionUtil {
    
    // Cron表达式正则验证
    private static final String CRON_REGEX = 
        "^\\s*($|#|\\w+\\s*=|(\\?|\\*|(?:[0-5]?\\d)(?:(?:-|\\/|\\,)(?:[0-5]?\\d))?(?:,(?:[0-5]?\\d)(?:(?:-|\\/|\\,)(?:[0-5]?\\d))?)*)\\s+(\\?|\\*|(?:[0-5]?\\d)(?:(?:-|\\/|\\,)(?:[0-5]?\\d))?(?:,(?:[0-5]?\\d)(?:(?:-|\\/|\\,)(?:[0-5]?\\d))?)*)\\s+(\\?|\\*|(?:[01]?\\d|2[0-3])(?:(?:-|\\/|\\,)(?:[01]?\\d|2[0-3]))?(?:,(?:[01]?\\d|2[0-3])(?:(?:-|\\/|\\,)(?:[01]?\\d|2[0-3]))?)*)\\s+(\\?|\\*|(?:0?[1-9]|[12]\\d|3[01])(?:(?:-|\\/|\\,)(?:0?[1-9]|[12]\\d|3[01]))?(?:,(?:0?[1-9]|[12]\\d|3[01])(?:(?:-|\\/|\\,)(?:0?[1-9]|[12]\\d|3[01]))?)*)\\s+(\\?|\\*|(?:[1-9]|1[012])(?:(?:-|\\/|\\,)(?:[1-9]|1[012]))?(?:L|W)?(?:,(?:[1-9]|1[012])(?:(?:-|\\/|\\,)(?:[1-9]|1[012]))?(?:L|W)?)*|\\?|\\*|(?:JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC)(?:(?:-)(?:JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC))?(?:,(?:JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC)(?:(?:-)(?:JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC))?)*)\\s+(\\?|\\*|(?:[0-6])(?:(?:-|\\/|\\,|#)(?:[0-6]))?(?:L)?(?:,(?:[0-6])(?:(?:-|\\/|\\,|#)(?:[0-6]))?(?:L)?)*|\\?|\\*|(?:MON|TUE|WED|THU|FRI|SAT|SUN)(?:(?:-)(?:MON|TUE|WED|THU|FRI|SAT|SUN))?(?:,(?:MON|TUE|WED|THU|FRI|SAT|SUN)(?:(?:-)(?:MON|TUE|WED|THU|FRI|SAT|SUN))?)*)$)";
    
    private static final Pattern CRON_PATTERN = Pattern.compile(CRON_REGEX);
    
    /**
     * 验证Cron表达式是否有效
     * 
     * @param cronExpression Cron表达式
     * @return 是否有效
     */
    public boolean isValidCronExpression(String cronExpression) {
        if (cronExpression == null || cronExpression.trim().isEmpty()) {
            return false;
        }
        
        try {
            // 简化验证：检查格式和字段数量
            String[] fields = cronExpression.trim().split("\\s+");
            
            // 支持6位或7位Cron表达式
            if (fields.length != 6 && fields.length != 7) {
                return false;
            }
            
            // 基本格式验证
            return validateCronFields(fields);
            
        } catch (Exception e) {
            log.warn("验证Cron表达式失败: {}", cronExpression, e);
            return false;
        }
    }
    
    /**
     * 验证Cron字段
     */
    private boolean validateCronFields(String[] fields) {
        try {
            // 秒 (0-59)
            if (!validateField(fields[0], 0, 59)) return false;
            
            // 分 (0-59)
            if (!validateField(fields[1], 0, 59)) return false;
            
            // 时 (0-23)
            if (!validateField(fields[2], 0, 23)) return false;
            
            // 日 (1-31)
            if (!validateDayField(fields[3])) return false;
            
            // 月 (1-12)
            if (!validateMonthField(fields[4])) return false;
            
            // 周 (0-7, 0和7都表示周日)
            if (!validateWeekField(fields[5])) return false;
            
            // 年 (可选，1970-3000)
            if (fields.length == 7 && !validateYearField(fields[6])) return false;
            
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 验证通用字段
     */
    private boolean validateField(String field, int min, int max) {
        if ("*".equals(field) || "?".equals(field)) {
            return true;
        }
        
        // 处理逗号分隔的值
        String[] values = field.split(",");
        for (String value : values) {
            if (!validateSingleValue(value.trim(), min, max)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 验证单个值
     */
    private boolean validateSingleValue(String value, int min, int max) {
        try {
            // 处理范围 (例如: 1-5)
            if (value.contains("-")) {
                String[] range = value.split("-");
                if (range.length != 2) return false;
                int start = Integer.parseInt(range[0]);
                int end = Integer.parseInt(range[1]);
                return start >= min && end <= max && start <= end;
            }
            
            // 处理步长 (例如: */5, 1-10/2)
            if (value.contains("/")) {
                String[] step = value.split("/");
                if (step.length != 2) return false;
                
                String baseValue = step[0];
                int stepValue = Integer.parseInt(step[1]);
                
                if ("*".equals(baseValue)) {
                    return stepValue > 0;
                } else {
                    return validateSingleValue(baseValue, min, max) && stepValue > 0;
                }
            }
            
            // 普通数值
            int intValue = Integer.parseInt(value);
            return intValue >= min && intValue <= max;
            
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * 验证日字段
     */
    private boolean validateDayField(String field) {
        if ("*".equals(field) || "?".equals(field)) {
            return true;
        }
        
        // 处理L (最后一天)
        if (field.endsWith("L")) {
            if ("L".equals(field)) return true;
            String dayPart = field.substring(0, field.length() - 1);
            return validateSingleValue(dayPart, 1, 31);
        }
        
        // 处理W (工作日)
        if (field.endsWith("W")) {
            String dayPart = field.substring(0, field.length() - 1);
            return validateSingleValue(dayPart, 1, 31);
        }
        
        return validateField(field, 1, 31);
    }
    
    /**
     * 验证月字段
     */
    private boolean validateMonthField(String field) {
        if ("*".equals(field) || "?".equals(field)) {
            return true;
        }
        
        // 处理月份名称
        String[] monthNames = {"JAN", "FEB", "MAR", "APR", "MAY", "JUN",
                              "JUL", "AUG", "SEP", "OCT", "NOV", "DEC"};
        
        String upperField = field.toUpperCase();
        for (String monthName : monthNames) {
            if (upperField.contains(monthName)) {
                return true;
            }
        }
        
        return validateField(field, 1, 12);
    }
    
    /**
     * 验证周字段
     */
    private boolean validateWeekField(String field) {
        if ("*".equals(field) || "?".equals(field)) {
            return true;
        }
        
        // 处理周名称
        String[] weekNames = {"MON", "TUE", "WED", "THU", "FRI", "SAT", "SUN"};
        String upperField = field.toUpperCase();
        for (String weekName : weekNames) {
            if (upperField.contains(weekName)) {
                return true;
            }
        }
        
        // 处理L (最后一个周几)
        if (field.endsWith("L")) {
            String weekPart = field.substring(0, field.length() - 1);
            return validateSingleValue(weekPart, 0, 7);
        }
        
        // 处理# (第几个周几)
        if (field.contains("#")) {
            String[] parts = field.split("#");
            if (parts.length != 2) return false;
            return validateSingleValue(parts[0], 0, 7) && validateSingleValue(parts[1], 1, 5);
        }
        
        return validateField(field, 0, 7);
    }
    
    /**
     * 验证年字段
     */
    private boolean validateYearField(String field) {
        if ("*".equals(field) || "?".equals(field)) {
            return true;
        }
        
        return validateField(field, 1970, 3000);
    }
    
    /**
     * 获取Cron表达式的下次执行时间
     * 
     * @param cronExpression Cron表达式
     * @param count 获取的次数
     * @return 下次执行时间列表
     */
    public List<LocalDateTime> getNextFireTimes(String cronExpression, int count) {
        List<LocalDateTime> fireTimes = new ArrayList<>();
        
        if (!isValidCronExpression(cronExpression)) {
            return fireTimes;
        }
        
        try {
            // 简化实现：生成一些示例时间
            LocalDateTime now = LocalDateTime.now();
            
            // 根据Cron表达式类型生成不同的时间
            if (cronExpression.contains("0 0 2 * * ?")) {
                // 每日2点执行
                for (int i = 0; i < count; i++) {
                    fireTimes.add(now.plusDays(i + 1).withHour(2).withMinute(0).withSecond(0));
                }
            } else if (cronExpression.contains("0 0 * * * ?")) {
                // 每小时执行
                for (int i = 0; i < count; i++) {
                    fireTimes.add(now.plusHours(i + 1).withMinute(0).withSecond(0));
                }
            } else if (cronExpression.contains("0 */5 * * * ?")) {
                // 每5分钟执行
                for (int i = 0; i < count; i++) {
                    fireTimes.add(now.plusMinutes((i + 1) * 5).withSecond(0));
                }
            } else {
                // 默认每小时执行
                for (int i = 0; i < count; i++) {
                    fireTimes.add(now.plusHours(i + 1));
                }
            }
            
        } catch (Exception e) {
            log.error("计算Cron表达式下次执行时间失败: {}", cronExpression, e);
        }
        
        return fireTimes;
    }
    
    /**
     * 获取Cron表达式描述
     * 
     * @param cronExpression Cron表达式
     * @return 描述文本
     */
    public String getCronDescription(String cronExpression) {
        if (!isValidCronExpression(cronExpression)) {
            return "无效的Cron表达式";
        }
        
        try {
            String[] fields = cronExpression.trim().split("\\s+");
            
            // 简化的描述生成
            if ("0 0 2 * * ?".equals(cronExpression)) {
                return "每日凌晨2点执行";
            } else if ("0 0 * * * ?".equals(cronExpression)) {
                return "每小时执行";
            } else if ("0 */5 * * * ?".equals(cronExpression)) {
                return "每5分钟执行";
            } else if ("0 0 0 * * ?".equals(cronExpression)) {
                return "每日午夜执行";
            } else if ("0 0 12 * * ?".equals(cronExpression)) {
                return "每日中午12点执行";
            } else {
                return "自定义调度: " + cronExpression;
            }
            
        } catch (Exception e) {
            log.error("生成Cron表达式描述失败: {}", cronExpression, e);
            return "无法解析的Cron表达式";
        }
    }
    
    /**
     * 获取常用的Cron表达式模板
     * 
     * @return Cron表达式模板列表
     */
    public List<Map<String, String>> getCronTemplates() {
        List<Map<String, String>> templates = new ArrayList<>();
        
        templates.add(createTemplate("每分钟执行", "0 * * * * ?"));
        templates.add(createTemplate("每5分钟执行", "0 */5 * * * ?"));
        templates.add(createTemplate("每15分钟执行", "0 */15 * * * ?"));
        templates.add(createTemplate("每30分钟执行", "0 */30 * * * ?"));
        templates.add(createTemplate("每小时执行", "0 0 * * * ?"));
        templates.add(createTemplate("每日凌晨2点执行", "0 0 2 * * ?"));
        templates.add(createTemplate("每日中午12点执行", "0 0 12 * * ?"));
        templates.add(createTemplate("每周一凌晨2点执行", "0 0 2 ? * MON"));
        templates.add(createTemplate("每月1号凌晨2点执行", "0 0 2 1 * ?"));
        
        return templates;
    }
    
    /**
     * 创建模板
     */
    private Map<String, String> createTemplate(String description, String expression) {
        Map<String, String> template = new HashMap<>();
        template.put("description", description);
        template.put("expression", expression);
        return template;
    }
}
