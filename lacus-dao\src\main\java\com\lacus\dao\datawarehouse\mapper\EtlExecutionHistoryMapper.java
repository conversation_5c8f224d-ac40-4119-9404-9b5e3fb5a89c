package com.lacus.dao.datawarehouse.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lacus.dao.datawarehouse.entity.EtlExecutionHistoryEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * ETL执行历史表 Mapper 接口
 */
@Mapper
public interface EtlExecutionHistoryMapper extends BaseMapper<EtlExecutionHistoryEntity> {

    /**
     * 分页查询执行历史
     */
    IPage<EtlExecutionHistoryEntity> selectExecutionHistoryPage(Page<EtlExecutionHistoryEntity> page, @Param("params") Map<String, Object> params);

    /**
     * 根据执行ID查询执行记录
     */
    EtlExecutionHistoryEntity selectByExecutionId(@Param("executionId") String executionId);

    /**
     * 根据任务ID查询执行历史
     */
    List<EtlExecutionHistoryEntity> selectByTaskId(@Param("taskId") Long taskId);

    /**
     * 根据执行状态查询历史
     */
    List<EtlExecutionHistoryEntity> selectByStatus(@Param("status") String status);

    /**
     * 获取任务最近的执行记录
     */
    EtlExecutionHistoryEntity selectLatestByTaskId(@Param("taskId") Long taskId);

    /**
     * 获取执行统计信息
     */
    Map<String, Object> selectExecutionStats(@Param("taskId") Long taskId);

    /**
     * 获取今日执行统计
     */
    Map<String, Object> selectTodayStats();

    /**
     * 清理历史数据
     */
    int deleteOldRecords(@Param("days") Integer days);

    /**
     * 获取执行趋势数据
     */
    List<Map<String, Object>> selectExecutionTrend(@Param("taskId") Long taskId, @Param("days") Integer days);
}
