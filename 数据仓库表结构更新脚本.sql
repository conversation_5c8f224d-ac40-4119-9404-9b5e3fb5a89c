-- 数据仓库表结构更新脚本
-- 用于添加缺失的字段以支持新的功能

-- 1. 更新ETL执行历史表，添加缺失字段
ALTER TABLE etl_execution_history 
ADD COLUMN execution_status VARCHAR(20) COMMENT '执行状态（新字段）' AFTER status,
ADD COLUMN execution_time BIGINT COMMENT '执行时间（毫秒）' AFTER duration,
ADD COLUMN processed_rows BIGINT COMMENT '处理行数' AFTER processed_records,
ADD COLUMN execution_log TEXT COMMENT '执行日志' AFTER execution_params;

-- 2. 更新质量监控表，确保所有字段存在
ALTER TABLE quality_monitor 
ADD COLUMN IF NOT EXISTS rule_count INT DEFAULT 0 COMMENT '规则总数',
ADD COLUMN IF NOT EXISTS failed_rule_count INT DEFAULT 0 COMMENT '失败规则数',
ADD COLUMN IF NOT EXISTS record_count BIGINT DEFAULT 0 COMMENT '记录总数',
ADD COLUMN IF NOT EXISTS last_check_time DATETIME COMMENT '最后检查时间',
ADD COLUMN IF NOT EXISTS check_frequency VARCHAR(50) DEFAULT 'DAILY' COMMENT '检查频率';

-- 3. 更新调度任务表，确保所有字段存在
ALTER TABLE schedule_job 
ADD COLUMN IF NOT EXISTS fire_count BIGINT DEFAULT 0 COMMENT '执行次数',
ADD COLUMN IF NOT EXISTS fail_count INT DEFAULT 0 COMMENT '失败次数',
ADD COLUMN IF NOT EXISTS current_retry_count INT DEFAULT 0 COMMENT '当前重试次数',
ADD COLUMN IF NOT EXISTS max_retry_count INT DEFAULT 3 COMMENT '最大重试次数',
ADD COLUMN IF NOT EXISTS last_execution_result TEXT COMMENT '最后执行结果';

-- 4. 创建ETL任务依赖关系表（如果不存在）
CREATE TABLE IF NOT EXISTS etl_task_dependency (
    dependency_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '依赖ID',
    task_id BIGINT NOT NULL COMMENT '任务ID',
    dependent_task_id BIGINT NOT NULL COMMENT '依赖的任务ID',
    dependency_type VARCHAR(20) DEFAULT 'SEQUENTIAL' COMMENT '依赖类型：SEQUENTIAL-顺序依赖，PARALLEL-并行依赖',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_task_id (task_id),
    INDEX idx_dependent_task_id (dependent_task_id),
    UNIQUE KEY uk_task_dependency (task_id, dependent_task_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='ETL任务依赖关系表';

-- 5. 创建数据血缘关系表（如果不存在）
CREATE TABLE IF NOT EXISTS data_lineage (
    lineage_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '血缘ID',
    source_table VARCHAR(100) NOT NULL COMMENT '源表名',
    source_layer VARCHAR(20) NOT NULL COMMENT '源数据层级',
    target_table VARCHAR(100) NOT NULL COMMENT '目标表名',
    target_layer VARCHAR(20) NOT NULL COMMENT '目标数据层级',
    task_id BIGINT COMMENT '关联的ETL任务ID',
    lineage_type VARCHAR(20) DEFAULT 'ETL' COMMENT '血缘类型：ETL-ETL任务，VIEW-视图，PROCEDURE-存储过程',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_source_table (source_table, source_layer),
    INDEX idx_target_table (target_table, target_layer),
    INDEX idx_task_id (task_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据血缘关系表';

-- 6. 创建质量规则模板表（如果不存在）
CREATE TABLE IF NOT EXISTS quality_rule_template (
    template_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '模板ID',
    template_name VARCHAR(100) NOT NULL COMMENT '模板名称',
    rule_type VARCHAR(50) NOT NULL COMMENT '规则类型',
    rule_description TEXT COMMENT '规则描述',
    rule_config JSON COMMENT '规则配置模板',
    applicable_layers VARCHAR(100) COMMENT '适用的数据层级',
    severity VARCHAR(20) DEFAULT 'MEDIUM' COMMENT '严重程度：LOW-低，MEDIUM-中，HIGH-高，CRITICAL-严重',
    is_builtin TINYINT(1) DEFAULT 0 COMMENT '是否内置模板',
    status TINYINT(1) DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_rule_type (rule_type),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='质量规则模板表';

-- 7. 插入内置质量规则模板
INSERT INTO quality_rule_template (template_name, rule_type, rule_description, rule_config, applicable_layers, severity, is_builtin) VALUES
('空值检查', 'NULL_CHECK', '检查字段中的空值数量', '{"threshold": 0, "fieldName": "", "description": "检查指定字段的空值数量"}', 'ODS,DWD,DWS,ADS', 'HIGH', 1),
('重复值检查', 'DUPLICATE_CHECK', '检查字段中的重复值数量', '{"threshold": 0, "fieldName": "", "description": "检查指定字段的重复值数量"}', 'ODS,DWD,DWS,ADS', 'MEDIUM', 1),
('数值范围检查', 'RANGE_CHECK', '检查数值字段是否在指定范围内', '{"minValue": 0, "maxValue": 100, "fieldName": "", "threshold": 0, "description": "检查数值字段的取值范围"}', 'ODS,DWD,DWS,ADS', 'MEDIUM', 1),
('格式检查', 'FORMAT_CHECK', '检查字段格式是否符合正则表达式', '{"pattern": "", "fieldName": "", "threshold": 0, "description": "检查字段格式是否符合指定模式"}', 'ODS,DWD,DWS,ADS', 'LOW', 1),
('记录数检查', 'RECORD_COUNT_CHECK', '检查表的记录数是否在合理范围内', '{"minCount": 0, "maxCount": 1000000, "description": "检查表的记录数量"}', 'ODS,DWD,DWS,ADS', 'HIGH', 1);

-- 8. 创建系统配置表（如果不存在）
CREATE TABLE IF NOT EXISTS system_config (
    config_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type VARCHAR(20) DEFAULT 'STRING' COMMENT '配置类型：STRING-字符串，NUMBER-数字，BOOLEAN-布尔，JSON-JSON对象',
    config_group VARCHAR(50) DEFAULT 'SYSTEM' COMMENT '配置分组',
    description TEXT COMMENT '配置描述',
    is_encrypted TINYINT(1) DEFAULT 0 COMMENT '是否加密存储',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_config_group (config_group)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 9. 插入默认系统配置
INSERT INTO system_config (config_key, config_value, config_type, config_group, description) VALUES
('etl.max_concurrent_tasks', '10', 'NUMBER', 'ETL', 'ETL最大并发任务数'),
('etl.task_timeout_minutes', '60', 'NUMBER', 'ETL', 'ETL任务超时时间（分钟）'),
('etl.retry_max_count', '3', 'NUMBER', 'ETL', 'ETL任务最大重试次数'),
('quality.check_frequency', 'DAILY', 'STRING', 'QUALITY', '质量检查默认频率'),
('quality.alert_threshold', '80', 'NUMBER', 'QUALITY', '质量告警阈值（分数）'),
('schedule.max_concurrent_jobs', '5', 'NUMBER', 'SCHEDULE', '调度最大并发任务数'),
('doris.connection_timeout', '30', 'NUMBER', 'DATABASE', 'Doris连接超时时间（秒）'),
('doris.query_timeout', '300', 'NUMBER', 'DATABASE', 'Doris查询超时时间（秒）');

-- 10. 创建操作日志表（如果不存在）
CREATE TABLE IF NOT EXISTS operation_log (
    log_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
    operation_module VARCHAR(50) NOT NULL COMMENT '操作模块',
    operation_desc TEXT COMMENT '操作描述',
    request_params TEXT COMMENT '请求参数',
    response_result TEXT COMMENT '响应结果',
    user_id BIGINT COMMENT '操作用户ID',
    user_name VARCHAR(50) COMMENT '操作用户名',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    execution_time BIGINT COMMENT '执行时间（毫秒）',
    status VARCHAR(20) DEFAULT 'SUCCESS' COMMENT '执行状态：SUCCESS-成功，FAILED-失败',
    error_message TEXT COMMENT '错误信息',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_operation_type (operation_type),
    INDEX idx_operation_module (operation_module),
    INDEX idx_user_id (user_id),
    INDEX idx_create_time (create_time),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';

-- 11. 更新现有表的索引优化
-- ETL执行历史表索引优化
CREATE INDEX IF NOT EXISTS idx_etl_execution_status_time ON etl_execution_history (status, start_time);
CREATE INDEX IF NOT EXISTS idx_etl_execution_task_time ON etl_execution_history (task_id, start_time);

-- 质量监控表索引优化
CREATE INDEX IF NOT EXISTS idx_quality_monitor_score_time ON quality_monitor (quality_score, last_check_time);
CREATE INDEX IF NOT EXISTS idx_quality_monitor_layer_score ON quality_monitor (data_layer, quality_score);

-- 调度任务表索引优化
CREATE INDEX IF NOT EXISTS idx_schedule_job_next_fire ON schedule_job (schedule_status, next_fire_time);
CREATE INDEX IF NOT EXISTS idx_schedule_job_last_fire ON schedule_job (last_fire_time);

-- 12. 创建视图用于统计查询
-- ETL任务执行统计视图
CREATE OR REPLACE VIEW v_etl_execution_stats AS
SELECT 
    DATE(start_time) as execution_date,
    COUNT(*) as total_executions,
    SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as success_count,
    SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed_count,
    SUM(CASE WHEN status = 'RUNNING' THEN 1 ELSE 0 END) as running_count,
    AVG(duration) as avg_duration,
    SUM(processed_records) as total_processed_records
FROM etl_execution_history 
WHERE start_time >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY DATE(start_time)
ORDER BY execution_date DESC;

-- 质量监控统计视图
CREATE OR REPLACE VIEW v_quality_monitor_stats AS
SELECT 
    data_layer,
    COUNT(*) as total_tables,
    SUM(CASE WHEN quality_status = 'PASSED' THEN 1 ELSE 0 END) as passed_tables,
    SUM(CASE WHEN quality_status = 'WARNING' THEN 1 ELSE 0 END) as warning_tables,
    SUM(CASE WHEN quality_status = 'FAILED' THEN 1 ELSE 0 END) as failed_tables,
    AVG(quality_score) as avg_quality_score,
    MAX(last_check_time) as latest_check_time
FROM quality_monitor 
WHERE status = 1 AND deleted = 0
GROUP BY data_layer;

-- 调度任务统计视图
CREATE OR REPLACE VIEW v_schedule_job_stats AS
SELECT 
    schedule_status,
    COUNT(*) as job_count,
    SUM(fire_count) as total_fire_count,
    SUM(fail_count) as total_fail_count,
    AVG(CASE WHEN fire_count > 0 THEN fail_count * 100.0 / fire_count ELSE 0 END) as avg_fail_rate
FROM schedule_job 
WHERE deleted = 0
GROUP BY schedule_status;

-- 13. 创建存储过程用于数据清理
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS CleanOldExecutionHistory(IN days_to_keep INT)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE affected_rows INT DEFAULT 0;
    
    -- 删除指定天数之前的执行历史记录
    DELETE FROM etl_execution_history 
    WHERE start_time < DATE_SUB(CURDATE(), INTERVAL days_to_keep DAY)
    AND status IN ('SUCCESS', 'FAILED', 'STOPPED');
    
    SET affected_rows = ROW_COUNT();
    
    -- 记录清理日志
    INSERT INTO operation_log (operation_type, operation_module, operation_desc, execution_time, create_time)
    VALUES ('DATA_CLEANUP', 'ETL', CONCAT('清理了 ', affected_rows, ' 条执行历史记录'), 0, NOW());
    
    SELECT CONCAT('成功清理了 ', affected_rows, ' 条执行历史记录') as result;
END //
DELIMITER ;

-- 执行完成提示
SELECT '数据仓库表结构更新完成！' as message;
