<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>ETL任务执行</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="refreshTaskList">刷新</el-button>
      </div>
      
      <!-- 任务选择区域 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true">
            <el-form-item label="任务名称" prop="taskName">
              <el-input
                v-model="queryParams.taskName"
                placeholder="请输入任务名称"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="数据层级" prop="targetLayer">
              <el-select v-model="queryParams.targetLayer" placeholder="请选择数据层级" clearable>
                <el-option label="ODS层" value="ods_db" />
                <el-option label="DWD层" value="dwd_db" />
                <el-option label="DWS层" value="dws_db" />
                <el-option label="ADS层" value="ads_db" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>

      <!-- 任务列表 -->
      <el-table v-loading="loading" :data="taskList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="任务名称" align="center" prop="taskName" />
        <el-table-column label="源层级" align="center" prop="sourceLayer" />
        <el-table-column label="目标层级" align="center" prop="targetLayer" />
        <el-table-column label="目标表" align="center" prop="targetTable" />
        <el-table-column label="写入模式" align="center" prop="writeMode">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.writeMode === 'APPEND'" type="success">追加</el-tag>
            <el-tag v-else-if="scope.row.writeMode === 'OVERWRITE'" type="warning">覆盖</el-tag>
            <el-tag v-else-if="scope.row.writeMode === 'UPSERT'" type="info">更新插入</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status === 'ENABLED'" type="success">启用</el-tag>
            <el-tag v-else type="danger">禁用</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-video-play"
              @click="handleExecute(scope.row)"
              :disabled="isTaskRunning(scope.row.taskId)"
            >执行</el-button>
            <el-button
              size="mini"
              type="warning"
              icon="el-icon-view"
              @click="handlePreview(scope.row)"
            >预览</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 执行监控区域 -->
    <el-card class="box-card" style="margin-top: 20px;" v-if="currentExecution">
      <div slot="header" class="clearfix">
        <span>执行监控</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="stopExecution">停止执行</el-button>
      </div>
      
      <el-row :gutter="20">
        <el-col :span="6">
          <el-statistic title="执行ID" :value="currentExecution.executionId" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="执行状态" :value="currentExecution.status" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="处理行数" :value="currentExecution.processedRows || 0" />
        </el-col>
        <el-col :span="6">
          <el-statistic title="执行时间" :value="formatDuration(currentExecution.executionTime)" />
        </el-col>
      </el-row>

      <!-- 执行进度 -->
      <el-progress 
        v-if="currentExecution.status === 'RUNNING'"
        :percentage="executionProgress" 
        :status="getProgressStatus()"
        style="margin-top: 20px;"
      />

      <!-- 执行日志 -->
      <el-tabs v-model="activeTab" style="margin-top: 20px;">
        <el-tab-pane label="执行日志" name="log">
          <el-input
            type="textarea"
            :rows="10"
            v-model="executionLog"
            readonly
            placeholder="执行日志将在这里显示..."
          />
        </el-tab-pane>
        <el-tab-pane label="执行指标" name="metrics">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="开始时间">{{ currentExecution.startTime }}</el-descriptions-item>
            <el-descriptions-item label="结束时间">{{ currentExecution.endTime || '执行中' }}</el-descriptions-item>
            <el-descriptions-item label="处理行数">{{ currentExecution.processedRows || 0 }}</el-descriptions-item>
            <el-descriptions-item label="执行时间">{{ formatDuration(currentExecution.executionTime) }}</el-descriptions-item>
            <el-descriptions-item label="平均速度">{{ calculateSpeed() }} 行/秒</el-descriptions-item>
            <el-descriptions-item label="执行状态">
              <el-tag :type="getStatusType(currentExecution.status)">{{ currentExecution.status }}</el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 预览对话框 -->
    <el-dialog title="ETL结果预览" :visible.sync="previewVisible" width="80%" :before-close="handleClosePreview">
      <el-table :data="previewData" v-loading="previewLoading" max-height="400">
        <el-table-column
          v-for="(column, index) in previewColumns"
          :key="index"
          :prop="column.prop"
          :label="column.label"
          show-overflow-tooltip
        />
      </el-table>
      <div style="margin-top: 20px;">
        <el-alert
          :title="`预览数据：共 ${previewInfo.previewRows} 行，${previewInfo.totalColumns} 列`"
          type="info"
          :closable="false"
        />
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="previewVisible = false">关 闭</el-button>
        <el-button type="primary" @click="confirmExecute">确认执行</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { 
  listEtlTask, 
  submitTask, 
  stopTask, 
  isTaskRunning, 
  previewEtlResult, 
  getExecutionLog, 
  getExecutionMetrics 
} from "@/api/datawarehouse/etl";

export default {
  name: "EtlExecution",
  data() {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        taskName: null,
        targetLayer: null
      },
      // 表格数据
      loading: true,
      taskList: [],
      total: 0,
      selectedTasks: [],
      runningTasks: new Set(),
      
      // 执行监控
      currentExecution: null,
      executionProgress: 0,
      executionLog: '',
      activeTab: 'log',
      pollingTimer: null,
      
      // 预览相关
      previewVisible: false,
      previewLoading: false,
      previewData: [],
      previewColumns: [],
      previewInfo: {},
      previewTask: null
    };
  },
  created() {
    this.getList();
  },
  beforeDestroy() {
    this.clearPolling();
  },
  methods: {
    /** 查询任务列表 */
    getList() {
      this.loading = true;
      listEtlTask(this.queryParams).then(response => {
        this.taskList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    
    /** 刷新任务列表 */
    refreshTaskList() {
      this.getList();
    },
    
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.selectedTasks = selection;
    },
    
    /** 检查任务是否正在运行 */
    isTaskRunning(taskId) {
      return this.runningTasks.has(taskId);
    },
    
    /** 执行任务 */
    handleExecute(row) {
      this.$confirm('确认执行ETL任务 "' + row.taskName + '"?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.executeTask(row);
      });
    },
    
    /** 执行任务逻辑 */
    executeTask(task) {
      const taskModel = {
        taskId: task.taskId,
        taskName: task.taskName,
        sourceLayer: task.sourceLayer,
        targetLayer: task.targetLayer,
        targetTable: task.targetTable,
        writeMode: task.writeMode || 'APPEND',
        sourceTablesConfig: task.sourceTablesConfig,
        fieldMappingsConfig: task.fieldMappingsConfig,
        primaryKeys: task.primaryKeys
      };
      
      submitTask(taskModel).then(response => {
        this.$message.success("ETL任务提交成功");
        this.currentExecution = {
          executionId: response.data,
          taskId: task.taskId,
          taskName: task.taskName,
          status: 'RUNNING',
          startTime: new Date().toLocaleString(),
          processedRows: 0,
          executionTime: 0
        };
        this.runningTasks.add(task.taskId);
        this.startPolling();
      }).catch(() => {
        this.$message.error("ETL任务提交失败");
      });
    },
    
    /** 预览任务 */
    handlePreview(row) {
      this.previewTask = row;
      this.previewVisible = true;
      this.previewLoading = true;
      
      const command = {
        taskName: row.taskName,
        sourceLayer: row.sourceLayer,
        targetLayer: row.targetLayer,
        targetTable: row.targetTable,
        sourceTablesConfig: row.sourceTablesConfig,
        fieldMappingsConfig: row.fieldMappingsConfig
      };
      
      previewEtlResult(command).then(response => {
        const result = response.data;
        this.previewData = result.previewData || [];
        this.previewInfo = {
          previewRows: result.previewRows || 0,
          totalColumns: result.totalColumns || 0
        };
        
        // 生成列配置
        if (this.previewData.length > 0) {
          this.previewColumns = Object.keys(this.previewData[0]).map(key => ({
            prop: key,
            label: key
          }));
        }
        
        this.previewLoading = false;
      }).catch(() => {
        this.$message.error("预览失败");
        this.previewLoading = false;
      });
    },
    
    /** 确认执行 */
    confirmExecute() {
      this.previewVisible = false;
      if (this.previewTask) {
        this.executeTask(this.previewTask);
      }
    },
    
    /** 关闭预览 */
    handleClosePreview() {
      this.previewVisible = false;
      this.previewTask = null;
      this.previewData = [];
      this.previewColumns = [];
    },
    
    /** 停止执行 */
    stopExecution() {
      if (this.currentExecution) {
        stopTask(this.currentExecution.taskId).then(() => {
          this.$message.success("任务停止成功");
          this.runningTasks.delete(this.currentExecution.taskId);
          this.clearPolling();
        });
      }
    },
    
    /** 开始轮询 */
    startPolling() {
      this.clearPolling();
      this.pollingTimer = setInterval(() => {
        this.updateExecutionStatus();
      }, 2000);
    },
    
    /** 清除轮询 */
    clearPolling() {
      if (this.pollingTimer) {
        clearInterval(this.pollingTimer);
        this.pollingTimer = null;
      }
    },
    
    /** 更新执行状态 */
    updateExecutionStatus() {
      if (!this.currentExecution) return;
      
      // 获取执行指标
      getExecutionMetrics(this.currentExecution.executionId).then(response => {
        const metrics = response.data;
        this.currentExecution = { ...this.currentExecution, ...metrics };
        
        if (metrics.executionStatus !== 'RUNNING') {
          this.runningTasks.delete(this.currentExecution.taskId);
          this.clearPolling();
        }
      });
      
      // 获取执行日志
      getExecutionLog(this.currentExecution.executionId).then(response => {
        this.executionLog = response.data;
      });
    },
    
    /** 格式化持续时间 */
    formatDuration(milliseconds) {
      if (!milliseconds) return '0ms';
      if (milliseconds < 1000) return milliseconds + 'ms';
      if (milliseconds < 60000) return Math.round(milliseconds / 1000) + 's';
      return Math.round(milliseconds / 60000) + 'm';
    },
    
    /** 计算处理速度 */
    calculateSpeed() {
      if (!this.currentExecution || !this.currentExecution.executionTime || !this.currentExecution.processedRows) {
        return 0;
      }
      const seconds = this.currentExecution.executionTime / 1000;
      return Math.round(this.currentExecution.processedRows / seconds);
    },
    
    /** 获取进度状态 */
    getProgressStatus() {
      if (!this.currentExecution) return '';
      switch (this.currentExecution.status) {
        case 'RUNNING': return '';
        case 'SUCCESS': return 'success';
        case 'FAILED': return 'exception';
        default: return '';
      }
    },
    
    /** 获取状态类型 */
    getStatusType(status) {
      switch (status) {
        case 'RUNNING': return 'primary';
        case 'SUCCESS': return 'success';
        case 'FAILED': return 'danger';
        case 'STOPPED': return 'warning';
        default: return 'info';
      }
    }
  }
};
</script>

<style scoped>
.box-card {
  margin-bottom: 20px;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
</style>
