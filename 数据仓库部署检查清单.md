# 数据仓库部署检查清单

## 前置条件检查

### 1. 环境要求
- [ ] JDK 8+ 已安装
- [ ] MySQL 5.7+ 已安装并运行
- [ ] Apache Doris 已安装并运行
- [ ] Redis (可选，用于缓存)

### 2. 数据库准备
- [ ] MySQL数据库已创建 (lacus)
- [ ] Doris数据库已创建 (ods_db, dwd_db, dws_db, ads_db)
- [ ] 数据库连接配置正确
- [ ] 数据库用户权限充足

### 3. 网络连接
- [ ] 应用服务器可以连接MySQL
- [ ] 应用服务器可以连接Doris
- [ ] 前端可以访问后端API

## 代码部署检查

### 1. 后端代码
- [ ] 所有Controller类已实现
  - [ ] EtlTaskController
  - [ ] QualityMonitorController  
  - [ ] ScheduleController
- [ ] 所有Service实现类已完成
  - [ ] EtlExecutionServiceImpl
  - [ ] QualityMonitorServiceImpl
  - [ ] ScheduleServiceImpl
- [ ] 工具类已添加
  - [ ] EtlSqlGenerator
  - [ ] DorisExecutionEngine
  - [ ] DataQualityCheckService
  - [ ] CronExpressionUtil
- [ ] 配置类已添加
  - [ ] EtlAsyncConfig

### 2. 数据访问层
- [ ] Mapper接口方法完整
  - [ ] EtlExecutionHistoryMapper.selectByExecutionId
  - [ ] 其他必要的Mapper方法
- [ ] XML映射文件已更新
  - [ ] 新增字段已添加到ResultMap
  - [ ] SQL语句已更新

### 3. 实体类
- [ ] EtlExecutionHistoryEntity字段完整
  - [ ] executionStatus字段
  - [ ] executionTime字段
  - [ ] processedRows字段
  - [ ] executionLog字段
- [ ] 其他实体类字段检查

## 数据库脚本执行

### 1. 表结构更新
- [ ] 执行 `数据仓库表结构更新脚本.sql`
- [ ] 验证新字段已添加
  ```sql
  -- 检查ETL执行历史表
  DESC etl_execution_history;
  
  -- 检查质量监控表
  DESC quality_monitor;
  
  -- 检查调度任务表
  DESC schedule_job;
  ```

### 2. 基础数据初始化
- [ ] 质量规则模板已插入
- [ ] 系统配置已插入
- [ ] 索引已创建
- [ ] 视图已创建

### 3. 数据验证
- [ ] 表结构正确
- [ ] 索引有效
- [ ] 约束正确
- [ ] 初始数据完整

## 配置文件检查

### 1. 应用配置
- [ ] application-datawarehouse.yml已添加
- [ ] 数据库连接配置正确
- [ ] 线程池配置合理
- [ ] 监控配置启用

### 2. 数据源配置
```yaml
# 检查Doris连接配置
datawarehouse:
  doris:
    connection-timeout: 30
    query-timeout: 300
    databases:
      ods: ods_db
      dwd: dwd_db
      dws: dws_db
      ads: ads_db
```

### 3. 日志配置
- [ ] 日志级别设置合理
- [ ] 日志文件路径正确
- [ ] 日志滚动策略配置

## 功能测试

### 1. ETL功能测试
- [ ] 创建ETL任务
- [ ] 配置源表和字段映射
- [ ] 预览ETL结果
- [ ] 提交任务执行
- [ ] 监控执行状态
- [ ] 查看执行日志
- [ ] 停止运行任务

### 2. 质量监控测试
- [ ] 创建质量监控配置
- [ ] 配置质量规则
- [ ] 执行质量检查
- [ ] 查看质量报告
- [ ] 质量评分计算
- [ ] 质量趋势分析

### 3. 调度管理测试
- [ ] 创建调度任务
- [ ] 配置Cron表达式
- [ ] 验证Cron表达式
- [ ] 启动调度任务
- [ ] 监控调度执行
- [ ] 查看调度历史

## API接口测试

### 1. ETL接口测试
```bash
# 提交ETL任务
curl -X POST http://localhost:8080/datawarehouse/etl/execution/submit \
  -H "Content-Type: application/json" \
  -d '{"taskId":1,"taskName":"测试任务"}'

# 停止ETL任务
curl -X POST http://localhost:8080/datawarehouse/etl/execution/stop \
  -H "Content-Type: application/json" \
  -d '{"taskId":1}'

# 预览ETL结果
curl -X POST http://localhost:8080/datawarehouse/etl/preview \
  -H "Content-Type: application/json" \
  -d '{"sourceLayer":"ods_db","targetLayer":"dwd_db"}'
```

### 2. 质量监控接口测试
```bash
# 执行质量检查
curl -X POST http://localhost:8080/datawarehouse/quality/check/1

# 获取质量统计
curl -X GET http://localhost:8080/datawarehouse/quality/stats
```

### 3. 调度管理接口测试
```bash
# 验证Cron表达式
curl -X POST http://localhost:8080/datawarehouse/schedule/validate-cron \
  -H "Content-Type: application/json" \
  -d '{"expression":"0 0 2 * * ?"}'

# 触发调度任务
curl -X POST http://localhost:8080/datawarehouse/schedule/trigger/1
```

## 性能测试

### 1. 并发测试
- [ ] ETL任务并发执行测试
- [ ] 质量检查并发测试
- [ ] 调度任务并发测试

### 2. 压力测试
- [ ] 大数据量ETL处理测试
- [ ] 长时间运行稳定性测试
- [ ] 内存使用情况监控

### 3. 响应时间测试
- [ ] API响应时间测试
- [ ] 数据库查询性能测试
- [ ] 页面加载时间测试

## 监控和告警

### 1. 系统监控
- [ ] JVM监控配置
- [ ] 数据库连接池监控
- [ ] 线程池监控
- [ ] 磁盘空间监控

### 2. 业务监控
- [ ] ETL任务执行监控
- [ ] 质量检查结果监控
- [ ] 调度任务状态监控

### 3. 告警配置
- [ ] 任务失败告警
- [ ] 系统异常告警
- [ ] 性能指标告警

## 安全检查

### 1. 权限控制
- [ ] 用户认证机制
- [ ] 接口权限控制
- [ ] 数据访问权限

### 2. 数据安全
- [ ] 敏感数据脱敏
- [ ] 数据传输加密
- [ ] 数据备份策略

### 3. 操作审计
- [ ] 操作日志记录
- [ ] 用户行为追踪
- [ ] 数据变更记录

## 文档和培训

### 1. 技术文档
- [ ] 部署文档完整
- [ ] API文档准确
- [ ] 故障排查指南

### 2. 用户文档
- [ ] 用户操作手册
- [ ] 功能使用说明
- [ ] 常见问题解答

### 3. 培训材料
- [ ] 系统培训PPT
- [ ] 操作演示视频
- [ ] 最佳实践指南

## 上线准备

### 1. 备份策略
- [ ] 数据库备份计划
- [ ] 配置文件备份
- [ ] 代码版本管理

### 2. 回滚计划
- [ ] 回滚步骤明确
- [ ] 回滚测试验证
- [ ] 应急联系人

### 3. 上线检查
- [ ] 生产环境配置检查
- [ ] 数据迁移验证
- [ ] 功能验收测试
- [ ] 性能基准测试

## 上线后验证

### 1. 功能验证
- [ ] 核心功能正常
- [ ] 数据处理正确
- [ ] 接口响应正常

### 2. 性能验证
- [ ] 响应时间符合要求
- [ ] 系统资源使用正常
- [ ] 并发处理能力达标

### 3. 稳定性验证
- [ ] 连续运行无异常
- [ ] 内存无泄漏
- [ ] 日志无错误

## 问题记录

### 部署过程中遇到的问题
- [ ] 问题描述：
- [ ] 解决方案：
- [ ] 预防措施：

### 测试过程中发现的问题
- [ ] 问题描述：
- [ ] 解决方案：
- [ ] 回归测试：

---

**检查人员**: ___________  
**检查日期**: ___________  
**审核人员**: ___________  
**审核日期**: ___________
