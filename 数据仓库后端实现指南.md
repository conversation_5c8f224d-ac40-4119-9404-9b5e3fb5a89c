# 数据仓库后端实现指南

## 概述

本项目实现了完整的数据仓库后端服务，包括ETL任务管理、数据质量监控、调度管理等核心功能。支持ODS、DWD、DWS、ADS四层数据仓库架构，基于Apache Doris作为底层存储引擎。

## 架构设计

### 系统架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   Controller    │    │   Business      │
│   (Vue.js)      │◄──►│   控制层        │◄──►│   业务层        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                       ┌─────────────────┐    ┌─────────────────┐
                       │   Service       │    │   工具组件      │
                       │   服务层        │◄──►│   (Utils)       │
                       └─────────────────┘    └─────────────────┘
                                │                       │
                       ┌─────────────────┐    ┌─────────────────┐
                       │   DAO/Mapper    │    │   执行引擎      │
                       │   数据访问层    │◄──►│   (Engines)     │
                       └─────────────────┘    └─────────────────┘
                                │                       │
                       ┌─────────────────┐    ┌─────────────────┐
                       │   MySQL         │    │   Apache Doris  │
                       │   (元数据)      │    │   (数据存储)    │
                       └─────────────────┘    └─────────────────┘
```

### 数据仓库分层
- **ODS层 (ods_db)**: 原始数据层，存储从各种数据源采集的原始数据
- **DWD层 (dwd_db)**: 数据仓库明细层，对ODS层数据进行清洗和标准化
- **DWS层 (dws_db)**: 数据仓库汇总层，基于DWD层进行轻度汇总
- **ADS层 (ads_db)**: 应用数据服务层，面向具体业务应用的数据集市

## 核心功能模块

### 1. ETL任务管理 (EtlTaskController)

#### 主要功能
- **任务配置管理**: 创建、修改、删除ETL任务配置
- **任务执行控制**: 提交、停止、监控ETL任务执行
- **结果预览**: 在正式执行前预览ETL处理结果
- **字段映射**: 智能生成源表到目标表的字段映射建议
- **数据血缘**: 追踪数据的来源和去向关系
- **依赖管理**: 管理任务间的依赖关系

#### 核心API
```java
// 提交ETL任务执行
@PostMapping("/execution/submit")
public R<String> submitTask(@RequestBody EtlTaskModel model)

// 停止ETL任务
@PostMapping("/execution/stop")
public R<Void> stopTask(@RequestBody Map<String, Long> params)

// 预览ETL结果
@PostMapping("/preview")
public R<Object> previewResult(@RequestBody EtlTaskAddCommand command)

// 获取字段映射建议
@PostMapping("/mapping-suggestions")
public R<Object> getFieldMappingSuggestions(@RequestBody Map<String, Object> params)
```

### 2. 数据质量监控 (QualityMonitorController)

#### 主要功能
- **质量规则配置**: 定义各种数据质量检查规则
- **质量检查执行**: 自动或手动执行质量检查
- **质量评分**: 基于检查结果计算质量得分
- **趋势分析**: 分析数据质量变化趋势
- **异常告警**: 质量问题自动告警通知

#### 支持的质量规则
- **空值检查 (NULL_CHECK)**: 检查字段空值数量
- **重复值检查 (DUPLICATE_CHECK)**: 检查字段重复值
- **范围检查 (RANGE_CHECK)**: 检查数值字段取值范围
- **格式检查 (FORMAT_CHECK)**: 检查字段格式是否符合正则表达式
- **自定义SQL检查 (CUSTOM_SQL)**: 执行自定义SQL进行检查

### 3. 调度管理 (ScheduleController)

#### 主要功能
- **调度任务配置**: 创建和管理定时调度任务
- **Cron表达式管理**: 支持复杂的时间调度配置
- **任务监控**: 实时监控调度任务执行状态
- **失败重试**: 自动重试失败的调度任务
- **依赖调度**: 支持基于依赖关系的任务调度

#### 调度类型
- **手动调度 (MANUAL)**: 手动触发执行
- **定时调度 (CRON)**: 基于Cron表达式定时执行
- **实时调度 (REALTIME)**: 实时数据处理

## 技术实现

### 核心组件

#### 1. EtlSqlGenerator - SQL生成器
```java
@Component
public class EtlSqlGenerator {
    // 根据ETL任务配置动态生成SQL语句
    public List<String> generateSql(Map<String, Object> taskConfig)
}
```

#### 2. DorisExecutionEngine - Doris执行引擎
```java
@Component
public class DorisExecutionEngine {
    // 执行SQL语句并返回执行结果
    public ExecutionResult executeStatements(List<String> sqlStatements)
}
```

#### 3. DataQualityCheckService - 数据质量检查服务
```java
@Service
public class DataQualityCheckService {
    // 执行质量检查并返回检查结果
    public QualityCheckResult executeQualityCheck(String tableName, List<Map<String, Object>> qualityRules)
}
```

#### 4. CronExpressionUtil - Cron表达式工具
```java
@Component
public class CronExpressionUtil {
    // 验证Cron表达式有效性
    public boolean isValidCronExpression(String cronExpression)
    
    // 获取下次执行时间
    public List<LocalDateTime> getNextFireTimes(String cronExpression, int count)
}
```

### 异步处理配置

系统使用多个线程池来处理不同类型的任务：

```java
@Configuration
@EnableAsync
public class EtlAsyncConfig {
    @Bean("etlExecutor")
    public Executor etlExecutor() // ETL任务执行器
    
    @Bean("qualityExecutor") 
    public Executor qualityExecutor() // 质量监控执行器
    
    @Bean("scheduleExecutor")
    public Executor scheduleExecutor() // 调度任务执行器
}
```

## 数据库设计

### 核心表结构

#### 1. ETL执行历史表
```sql
CREATE TABLE etl_execution_history (
    execution_id VARCHAR(50) PRIMARY KEY,
    task_id BIGINT NOT NULL,
    task_name VARCHAR(100),
    status VARCHAR(20),
    execution_status VARCHAR(20),
    start_time DATETIME,
    end_time DATETIME,
    duration BIGINT,
    execution_time BIGINT,
    processed_records BIGINT,
    processed_rows BIGINT,
    error_message TEXT,
    execution_log TEXT,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. 质量监控表
```sql
CREATE TABLE quality_monitor (
    monitor_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    table_name VARCHAR(100) NOT NULL,
    data_layer VARCHAR(20),
    quality_score INT DEFAULT 0,
    quality_status VARCHAR(20),
    rule_count INT DEFAULT 0,
    failed_rule_count INT DEFAULT 0,
    last_check_time DATETIME,
    check_frequency VARCHAR(50) DEFAULT 'DAILY'
);
```

#### 3. 调度任务表
```sql
CREATE TABLE schedule_job (
    job_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    job_name VARCHAR(100) NOT NULL,
    cron_expression VARCHAR(100),
    schedule_status VARCHAR(20),
    fire_count BIGINT DEFAULT 0,
    fail_count INT DEFAULT 0,
    last_fire_time DATETIME,
    next_fire_time DATETIME,
    max_retry_count INT DEFAULT 3
);
```

## 部署配置

### 1. 数据库配置
```yaml
# application-datawarehouse.yml
datawarehouse:
  doris:
    connection-timeout: 30
    query-timeout: 300
    databases:
      ods: ods_db
      dwd: dwd_db
      dws: dws_db
      ads: ads_db
```

### 2. 线程池配置
```yaml
datawarehouse:
  thread-pool:
    etl-executor:
      core-pool-size: 5
      max-pool-size: 20
      queue-capacity: 100
```

### 3. 执行SQL脚本
运行 `数据仓库表结构更新脚本.sql` 来创建和更新数据库表结构。

## 使用指南

### 1. ETL任务执行流程
1. 创建ETL任务配置
2. 配置源表和字段映射
3. 预览执行结果
4. 提交任务执行
5. 监控执行状态
6. 查看执行日志和指标

### 2. 质量监控流程
1. 配置质量检查规则
2. 设置检查频率和阈值
3. 执行质量检查
4. 查看质量报告
5. 处理质量异常

### 3. 调度管理流程
1. 创建调度任务
2. 配置Cron表达式
3. 设置任务依赖
4. 启动调度
5. 监控执行状态

## 监控和运维

### 1. 性能监控
- 任务执行时间监控
- 数据处理量统计
- 系统资源使用情况
- 错误率和成功率统计

### 2. 告警机制
- ETL任务执行失败告警
- 数据质量检查失败告警
- 调度任务超时告警
- 系统异常告警

### 3. 日志管理
- 详细的执行日志记录
- 错误信息追踪
- 操作审计日志
- 性能指标日志

## 扩展开发

### 1. 添加新的质量规则
继承 `DataQualityCheckService` 并实现新的检查方法。

### 2. 支持新的数据源
扩展 `DorisExecutionEngine` 以支持其他数据库。

### 3. 自定义调度策略
实现新的调度器来支持复杂的调度需求。

## 故障排查

### 1. 常见问题
- ETL任务执行失败：检查SQL语法和数据源连接
- 质量检查异常：验证规则配置和表结构
- 调度任务不执行：检查Cron表达式和任务状态

### 2. 日志查看
```bash
# 查看ETL执行日志
tail -f logs/datawarehouse.log | grep "ETL"

# 查看质量检查日志
tail -f logs/datawarehouse.log | grep "QUALITY"

# 查看调度任务日志
tail -f logs/datawarehouse.log | grep "SCHEDULE"
```

## 总结

本数据仓库后端实现提供了完整的ETL处理、质量监控和调度管理功能，支持企业级数据仓库的运营需求。通过模块化设计和异步处理机制，确保了系统的高性能和可扩展性。
