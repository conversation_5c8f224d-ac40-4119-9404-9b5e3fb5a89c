<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
      <el-form-item label="任务名称" prop="taskName">
        <el-input v-model="queryParams.taskName" placeholder="请输入任务名称" clearable @keyup.enter="handleQuery"/>
      </el-form-item>
      <el-form-item label="源层级" prop="sourceLayer">
        <el-select v-model="queryParams.sourceLayer" placeholder="请选择源层级" clearable>
          <el-option label="ODS" value="ODS"/>
          <el-option label="DWD" value="DWD"/>
          <el-option label="DWS" value="DWS"/>
        </el-select>
      </el-form-item>
      <el-form-item label="目标层级" prop="targetLayer">
        <el-select v-model="queryParams.targetLayer" placeholder="请选择目标层级" clearable>
          <el-option label="DWD" value="DWD"/>
          <el-option label="DWS" value="DWS"/>
          <el-option label="ADS" value="ADS"/>
        </el-select>
      </el-form-item>
      <el-form-item label="任务状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="启用" value="1"/>
          <el-option label="禁用" value="0"/>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd">新增ETL任务</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="VideoPlay" @click="handleBatchRun" :disabled="multiple">批量执行</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" @click="handleBatchDelete" :disabled="multiple">批量删除</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="etlList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="任务名称" align="center" prop="taskName" :show-overflow-tooltip="true"/>
      <el-table-column label="任务描述" align="center" prop="description" :show-overflow-tooltip="true"/>
      <el-table-column label="数据流向" align="center" width="120">
        <template #default="scope">
          <el-tag type="info" size="small">{{ scope.row.sourceLayer }}</el-tag>
          <el-icon style="margin: 0 5px;"><Right/></el-icon>
          <el-tag type="success" size="small">{{ scope.row.targetLayer }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="源表数量" align="center" prop="sourceTableCount" width="100"/>
      <el-table-column label="目标表" align="center" prop="targetTable" :show-overflow-tooltip="true"/>
      <el-table-column label="调度方式" align="center" prop="scheduleType" width="100">
        <template #default="scope">
          <dict-tag :options="schedule_type" :value="scope.row.scheduleType"/>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template #default="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            @change="handleStatusChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="最后执行时间" align="center" prop="lastRunTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.lastRunTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="300" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button-group>
            <el-tooltip content="查看详情" placement="top">
              <el-button type="primary" icon="View" @click="handleDetail(scope.row)" size="small"/>
            </el-tooltip>
            <el-tooltip content="编辑配置" placement="top">
              <el-button type="warning" icon="Edit" @click="handleUpdate(scope.row)" size="small"/>
            </el-tooltip>
            <el-tooltip content="执行任务" placement="top">
              <el-button type="success" icon="VideoPlay" @click="handleRun(scope.row)" size="small"/>
            </el-tooltip>
            <el-tooltip content="查看日志" placement="top">
              <el-button type="info" icon="Document" @click="handleLog(scope.row)" size="small"/>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button type="danger" icon="Delete" @click="handleDelete(scope.row)" size="small"/>
            </el-tooltip>
          </el-button-group>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- ETL任务创建/编辑弹出层 -->
    <EtlTaskCreate
      v-model:visible="createDialogVisible"
      :task-id="currentTaskId"
      @success="handleCreateSuccess"
    />
  </div>
</template>

<script setup name="EtlTask">
import { listEtlTask, deleteEtlTask, updateEtlTaskStatus, runEtlTask } from '@/api/datawarehouse/etl';
import EtlTaskCreate from './create.vue';

const { proxy } = getCurrentInstance();
const { schedule_type } = proxy.useDict('schedule_type');
const router = useRouter();

const etlList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const createDialogVisible = ref(false);
const currentTaskId = ref(null);

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    taskName: undefined,
    sourceLayer: undefined,
    targetLayer: undefined,
    status: undefined
  }
});

const { queryParams } = toRefs(data);

/** 查询ETL任务列表 */
function getList() {
  loading.value = true;
  listEtlTask(queryParams.value).then(response => {
    etlList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  currentTaskId.value = null;
  createDialogVisible.value = true;
}

/** 修改按钮操作 */
function handleUpdate(row) {
  currentTaskId.value = row.taskId || ids.value;
  createDialogVisible.value = true;
}

/** 创建/编辑成功回调 */
function handleCreateSuccess() {
  createDialogVisible.value = false;
  getList();
}

/** 查看详情 */
function handleDetail(row) {
  router.push(`/datawarehouse/etl/detail/${row.id}`);
}

/** 状态修改 */
function handleStatusChange(row) {
  let text = row.status === 1 ? "启用" : "停用";
  proxy.$modal.confirm(`确认要"${text}""${row.taskName}"任务吗？`).then(() => {
    return updateEtlTaskStatus(row.id, row.status);
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功");
  }).catch(() => {
    row.status = row.status === 0 ? 1 : 0;
  });
}

/** 执行任务 */
function handleRun(row) {
  proxy.$modal.confirm(`确认要执行"${row.taskName}"任务吗？`).then(() => {
    return runEtlTask(row.id);
  }).then(() => {
    proxy.$modal.msgSuccess("任务执行成功");
    getList();
  });
}

/** 查看日志 */
function handleLog(row) {
  router.push(`/datawarehouse/etl/log/${row.id}`);
}

/** 删除按钮操作 */
function handleDelete(row) {
console.log(row)
  const taskIds = row.taskId || ids.value;
  proxy.$modal.confirm(`是否确认删除ETL任务编号为"${taskIds}"的数据项？`).then(() => {
    return deleteEtlTask(taskIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  });
}

/** 批量删除 */
function handleBatchDelete() {
  handleDelete();
}

/** 批量执行 */
function handleBatchRun() {
  proxy.$modal.confirm(`确认要批量执行选中的${ids.value.length}个任务吗？`).then(() => {
    return runEtlTask(ids.value.join(','));
  }).then(() => {
    proxy.$modal.msgSuccess("批量执行成功");
    getList();
  });
}

getList();
</script>
