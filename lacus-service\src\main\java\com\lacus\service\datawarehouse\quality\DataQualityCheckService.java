package com.lacus.service.datawarehouse.quality;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 数据质量检查服务
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DataQualityCheckService {

    @Autowired
    private JdbcTemplate dorisJdbcTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 执行质量检查
     *
     * @param tableName 表名
     * @param qualityRules 质量规则列表
     * @return 检查结果
     */
    public QualityCheckResult executeQualityCheck(String tableName, List<Map<String, Object>> qualityRules) {
        QualityCheckResult result = new QualityCheckResult();
        result.setTableName(tableName);
        result.setCheckTime(LocalDateTime.now());
        result.setTotalRules(qualityRules.size());

        List<RuleCheckResult> ruleResults = new ArrayList<>();
        int passedRules = 0;
        int failedRules = 0;

        try {
            log.info("开始执行数据质量检查，表名: {}, 规则数量: {}", tableName, qualityRules.size());

            for (Map<String, Object> rule : qualityRules) {
                RuleCheckResult ruleResult = executeRule(tableName, rule);
                ruleResults.add(ruleResult);

                if (ruleResult.isPassed()) {
                    passedRules++;
                } else {
                    failedRules++;
                }
            }

            result.setRuleResults(ruleResults);
            result.setPassedRules(passedRules);
            result.setFailedRules(failedRules);
            result.setPassRate((double) passedRules / qualityRules.size() * 100);

            // 计算质量得分
            int qualityScore = calculateQualityScore(ruleResults);
            result.setQualityScore(qualityScore);
            result.setQualityLevel(getQualityLevel(qualityScore));

            log.info("数据质量检查完成，表名: {}, 质量得分: {}, 通过率: {}%",
                tableName, qualityScore, result.getPassRate());

        } catch (Exception e) {
            log.error("数据质量检查失败，表名: {}", tableName, e);
            result.setErrorMessage(e.getMessage());
        }

        return result;
    }

    /**
     * 执行单个质量规则
     */
    private RuleCheckResult executeRule(String tableName, Map<String, Object> rule) {
        RuleCheckResult result = new RuleCheckResult();
        result.setRuleName((String) rule.get("ruleName"));
        result.setRuleType((String) rule.get("ruleType"));
        result.setDescription((String) rule.get("description"));

        try {
            String ruleType = (String) rule.get("ruleType");

            switch (ruleType.toUpperCase()) {
                case "NULL_CHECK":
                    result = executeNullCheck(tableName, rule);
                    break;
                case "DUPLICATE_CHECK":
                    result = executeDuplicateCheck(tableName, rule);
                    break;
                case "RANGE_CHECK":
                    result = executeRangeCheck(tableName, rule);
                    break;
                case "FORMAT_CHECK":
                    result = executeFormatCheck(tableName, rule);
                    break;
                case "CUSTOM_SQL":
                    result = executeCustomSqlCheck(tableName, rule);
                    break;
                default:
                    result.setPassed(false);
                    result.setErrorMessage("不支持的规则类型: " + ruleType);
            }

        } catch (Exception e) {
            log.error("执行质量规则失败，规则: {}", rule.get("ruleName"), e);
            result.setPassed(false);
            result.setErrorMessage(e.getMessage());
        }

        return result;
    }

    /**
     * 执行空值检查
     */
    private RuleCheckResult executeNullCheck(String tableName, Map<String, Object> rule) {
        RuleCheckResult result = new RuleCheckResult();
        result.setRuleName((String) rule.get("ruleName"));
        result.setRuleType("NULL_CHECK");

        String fieldName = (String) rule.get("fieldName");
        Integer threshold = (Integer) rule.get("threshold");

        String sql = String.format("SELECT COUNT(*) FROM %s WHERE %s IS NULL", tableName, fieldName);

        try {
            Integer nullCount = dorisJdbcTemplate.queryForObject(sql, Integer.class);
            result.setActualValue(nullCount);
            result.setExpectedValue(threshold);
            result.setPassed(nullCount <= threshold);
            result.setCheckSql(sql);

            if (!result.isPassed()) {
                result.setErrorMessage(String.format("字段 %s 存在 %d 个空值，超过阈值 %d", fieldName, nullCount, threshold));
            }

        } catch (Exception e) {
            result.setPassed(false);
            result.setErrorMessage("执行空值检查失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 执行重复值检查
     */
    private RuleCheckResult executeDuplicateCheck(String tableName, Map<String, Object> rule) {
        RuleCheckResult result = new RuleCheckResult();
        result.setRuleName((String) rule.get("ruleName"));
        result.setRuleType("DUPLICATE_CHECK");

        String fieldName = (String) rule.get("fieldName");
        Integer threshold = (Integer) rule.get("threshold");

        String sql = String.format(
            "SELECT COUNT(*) FROM (SELECT %s FROM %s GROUP BY %s HAVING COUNT(*) > 1) t",
            fieldName, tableName, fieldName);

        try {
            Integer duplicateCount = dorisJdbcTemplate.queryForObject(sql, Integer.class);
            result.setActualValue(duplicateCount);
            result.setExpectedValue(threshold);
            result.setPassed(duplicateCount <= threshold);
            result.setCheckSql(sql);

            if (!result.isPassed()) {
                result.setErrorMessage(String.format("字段 %s 存在 %d 个重复值，超过阈值 %d", fieldName, duplicateCount, threshold));
            }

        } catch (Exception e) {
            result.setPassed(false);
            result.setErrorMessage("执行重复值检查失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 执行范围检查
     */
    private RuleCheckResult executeRangeCheck(String tableName, Map<String, Object> rule) {
        RuleCheckResult result = new RuleCheckResult();
        result.setRuleName((String) rule.get("ruleName"));
        result.setRuleType("RANGE_CHECK");

        String fieldName = (String) rule.get("fieldName");
        Object minValue = rule.get("minValue");
        Object maxValue = rule.get("maxValue");
        Integer threshold = (Integer) rule.get("threshold");

        String sql = String.format(
            "SELECT COUNT(*) FROM %s WHERE %s NOT BETWEEN %s AND %s",
            tableName, fieldName, minValue, maxValue);

        try {
            Integer outOfRangeCount = dorisJdbcTemplate.queryForObject(sql, Integer.class);
            result.setActualValue(outOfRangeCount);
            result.setExpectedValue(threshold);
            result.setPassed(outOfRangeCount <= threshold);
            result.setCheckSql(sql);

            if (!result.isPassed()) {
                result.setErrorMessage(String.format("字段 %s 有 %d 个值超出范围 [%s, %s]，超过阈值 %d",
                    fieldName, outOfRangeCount, minValue, maxValue, threshold));
            }

        } catch (Exception e) {
            result.setPassed(false);
            result.setErrorMessage("执行范围检查失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 执行格式检查
     */
    private RuleCheckResult executeFormatCheck(String tableName, Map<String, Object> rule) {
        RuleCheckResult result = new RuleCheckResult();
        result.setRuleName((String) rule.get("ruleName"));
        result.setRuleType("FORMAT_CHECK");

        String fieldName = (String) rule.get("fieldName");
        String pattern = (String) rule.get("pattern");
        Integer threshold = (Integer) rule.get("threshold");

        String sql = String.format(
            "SELECT COUNT(*) FROM %s WHERE %s NOT REGEXP '%s'",
            tableName, fieldName, pattern);

        try {
            Integer invalidFormatCount = dorisJdbcTemplate.queryForObject(sql, Integer.class);
            result.setActualValue(invalidFormatCount);
            result.setExpectedValue(threshold);
            result.setPassed(invalidFormatCount <= threshold);
            result.setCheckSql(sql);

            if (!result.isPassed()) {
                result.setErrorMessage(String.format("字段 %s 有 %d 个值格式不符合正则表达式 %s，超过阈值 %d",
                    fieldName, invalidFormatCount, pattern, threshold));
            }

        } catch (Exception e) {
            result.setPassed(false);
            result.setErrorMessage("执行格式检查失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 执行自定义SQL检查
     */
    private RuleCheckResult executeCustomSqlCheck(String tableName, Map<String, Object> rule) {
        RuleCheckResult result = new RuleCheckResult();
        result.setRuleName((String) rule.get("ruleName"));
        result.setRuleType("CUSTOM_SQL");

        String customSql = (String) rule.get("customSql");
        Integer threshold = (Integer) rule.get("threshold");

        // 替换SQL中的表名占位符
        String sql = customSql.replace("{table}", tableName);

        try {
            Integer checkResult = dorisJdbcTemplate.queryForObject(sql, Integer.class);
            result.setActualValue(checkResult);
            result.setExpectedValue(threshold);
            result.setPassed(checkResult <= threshold);
            result.setCheckSql(sql);

            if (!result.isPassed()) {
                result.setErrorMessage(String.format("自定义检查结果 %d 超过阈值 %d", checkResult, threshold));
            }

        } catch (Exception e) {
            result.setPassed(false);
            result.setErrorMessage("执行自定义SQL检查失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 计算质量得分
     */
    private int calculateQualityScore(List<RuleCheckResult> ruleResults) {
        if (ruleResults.isEmpty()) {
            return 100;
        }

        int totalWeight = 0;
        int weightedScore = 0;

        for (RuleCheckResult ruleResult : ruleResults) {
            int weight = getWeight(ruleResult.getRuleType());
            totalWeight += weight;

            if (ruleResult.isPassed()) {
                weightedScore += weight * 100;
            }
        }

        return totalWeight > 0 ? weightedScore / totalWeight : 100;
    }

    /**
     * 获取规则权重
     */
    private int getWeight(String ruleType) {
        switch (ruleType.toUpperCase()) {
            case "NULL_CHECK":
                return 3;
            case "DUPLICATE_CHECK":
                return 2;
            case "RANGE_CHECK":
                return 2;
            case "FORMAT_CHECK":
                return 1;
            case "CUSTOM_SQL":
                return 2;
            default:
                return 1;
        }
    }

    /**
     * 获取质量等级
     */
    private String getQualityLevel(int score) {
        if (score >= 90) {
            return "EXCELLENT";
        } else if (score >= 80) {
            return "GOOD";
        } else if (score >= 60) {
            return "FAIR";
        } else {
            return "POOR";
        }
    }

    /**
     * 质量检查结果类
     */
    public static class QualityCheckResult {
        private String tableName;
        private LocalDateTime checkTime;
        private int totalRules;
        private int passedRules;
        private int failedRules;
        private double passRate;
        private int qualityScore;
        private String qualityLevel;
        private String errorMessage;
        private List<RuleCheckResult> ruleResults = new ArrayList<>();

        // getters and setters
        public String getTableName() { return tableName; }
        public void setTableName(String tableName) { this.tableName = tableName; }

        public LocalDateTime getCheckTime() { return checkTime; }
        public void setCheckTime(LocalDateTime checkTime) { this.checkTime = checkTime; }

        public int getTotalRules() { return totalRules; }
        public void setTotalRules(int totalRules) { this.totalRules = totalRules; }

        public int getPassedRules() { return passedRules; }
        public void setPassedRules(int passedRules) { this.passedRules = passedRules; }

        public int getFailedRules() { return failedRules; }
        public void setFailedRules(int failedRules) { this.failedRules = failedRules; }

        public double getPassRate() { return passRate; }
        public void setPassRate(double passRate) { this.passRate = passRate; }

        public int getQualityScore() { return qualityScore; }
        public void setQualityScore(int qualityScore) { this.qualityScore = qualityScore; }

        public String getQualityLevel() { return qualityLevel; }
        public void setQualityLevel(String qualityLevel) { this.qualityLevel = qualityLevel; }

        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }

        public List<RuleCheckResult> getRuleResults() { return ruleResults; }
        public void setRuleResults(List<RuleCheckResult> ruleResults) { this.ruleResults = ruleResults; }
    }

    /**
     * 规则检查结果类
     */
    public static class RuleCheckResult {
        private String ruleName;
        private String ruleType;
        private String description;
        private boolean passed;
        private Object actualValue;
        private Object expectedValue;
        private String checkSql;
        private String errorMessage;

        // getters and setters
        public String getRuleName() { return ruleName; }
        public void setRuleName(String ruleName) { this.ruleName = ruleName; }

        public String getRuleType() { return ruleType; }
        public void setRuleType(String ruleType) { this.ruleType = ruleType; }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }

        public boolean isPassed() { return passed; }
        public void setPassed(boolean passed) { this.passed = passed; }

        public Object getActualValue() { return actualValue; }
        public void setActualValue(Object actualValue) { this.actualValue = actualValue; }

        public Object getExpectedValue() { return expectedValue; }
        public void setExpectedValue(Object expectedValue) { this.expectedValue = expectedValue; }

        public String getCheckSql() { return checkSql; }
        public void setCheckSql(String checkSql) { this.checkSql = checkSql; }

        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    }
}
