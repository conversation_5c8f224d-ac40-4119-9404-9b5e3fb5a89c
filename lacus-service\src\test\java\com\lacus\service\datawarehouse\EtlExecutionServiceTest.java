package com.lacus.service.datawarehouse;

import com.lacus.dao.datawarehouse.entity.EtlExecutionHistoryEntity;
import com.lacus.dao.datawarehouse.mapper.EtlExecutionHistoryMapper;
import com.lacus.service.datawarehouse.command.EtlTaskAddCommand;
import com.lacus.service.datawarehouse.engine.DorisExecutionEngine;
import com.lacus.service.datawarehouse.impl.EtlExecutionServiceImpl;
import com.lacus.service.datawarehouse.model.EtlTaskModel;
import com.lacus.service.datawarehouse.util.EtlSqlGenerator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ETL执行服务测试类
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class EtlExecutionServiceTest {

    @Mock
    private EtlExecutionHistoryMapper executionHistoryMapper;

    @Mock
    private JdbcTemplate dorisJdbcTemplate;

    @Mock
    private EtlSqlGenerator etlSqlGenerator;

    @Mock
    private DorisExecutionEngine dorisExecutionEngine;

    @InjectMocks
    private EtlExecutionServiceImpl etlExecutionService;

    private EtlTaskModel testTaskModel;
    private EtlTaskAddCommand testCommand;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testTaskModel = new EtlTaskModel();
        testTaskModel.setTaskId(1L);
        testTaskModel.setTaskName("测试ETL任务");
        testTaskModel.setSourceLayer("ods_db");
        testTaskModel.setTargetLayer("dwd_db");
        testTaskModel.setTargetTable("dwd_test_table");
        testTaskModel.setWriteMode("APPEND");
        testTaskModel.setSourceTablesConfig("[{\"tableName\":\"ods_test_table\",\"alias\":\"t1\"}]");
        testTaskModel.setFieldMappingsConfig("[{\"sourceField\":\"id\",\"targetField\":\"id\",\"expression\":\"\"}]");

        testCommand = new EtlTaskAddCommand();
        testCommand.setTaskName("测试ETL任务");
        testCommand.setSourceLayer("ods_db");
        testCommand.setTargetLayer("dwd_db");
        testCommand.setTargetTable("dwd_test_table");
        testCommand.setSourceTablesConfig("[{\"tableName\":\"ods_test_table\"}]");
        testCommand.setFieldMappingsConfig("[{\"sourceField\":\"id\",\"targetField\":\"id\"}]");
    }

    @Test
    void testSubmitTask_Success() {
        // 准备Mock数据
        List<String> sqlStatements = Arrays.asList("INSERT INTO dwd_db.dwd_test_table SELECT * FROM ods_db.ods_test_table");
        DorisExecutionEngine.ExecutionResult executionResult = new DorisExecutionEngine.ExecutionResult();
        executionResult.setStatus("SUCCESS");
        executionResult.setProcessedRows(100L);
        executionResult.setExecutionTimeMs(5000L);

        // 配置Mock行为
        when(etlSqlGenerator.generateSql(any())).thenReturn(sqlStatements);
        when(dorisExecutionEngine.executeStatements(sqlStatements)).thenReturn(executionResult);
        when(executionHistoryMapper.insert(any(EtlExecutionHistoryEntity.class))).thenReturn(1);
        when(executionHistoryMapper.updateById(any(EtlExecutionHistoryEntity.class))).thenReturn(1);

        // 执行测试
        String executionId = etlExecutionService.submitTask(testTaskModel);

        // 验证结果
        assertNotNull(executionId);
        assertTrue(executionId.length() > 0);

        // 验证Mock调用
        verify(etlSqlGenerator, times(1)).generateSql(any());
        verify(dorisExecutionEngine, times(1)).executeStatements(sqlStatements);
        verify(executionHistoryMapper, times(1)).insert(any(EtlExecutionHistoryEntity.class));
    }

    @Test
    void testStopTask_Success() {
        // 准备Mock数据
        Long taskId = 1L;
        EtlExecutionHistoryEntity mockHistory = new EtlExecutionHistoryEntity();
        mockHistory.setExecutionId("test-execution-id");
        mockHistory.setStatus("RUNNING");

        // 配置Mock行为
        when(executionHistoryMapper.selectByExecutionId(anyString())).thenReturn(mockHistory);
        when(executionHistoryMapper.updateById(any(EtlExecutionHistoryEntity.class))).thenReturn(1);

        // 执行测试
        assertDoesNotThrow(() -> etlExecutionService.stopTask(taskId));

        // 验证Mock调用
        verify(executionHistoryMapper, times(1)).updateById(any(EtlExecutionHistoryEntity.class));
    }

    @Test
    void testPreviewResult_Success() {
        // 准备Mock数据
        List<Map<String, Object>> mockPreviewData = Arrays.asList(
            createMockRow("1", "张三", "25"),
            createMockRow("2", "李四", "30")
        );

        String previewSql = "SELECT id, name, age FROM ods_db.ods_test_table LIMIT 100";

        // 配置Mock行为
        when(dorisJdbcTemplate.queryForList(anyString())).thenReturn(mockPreviewData);

        // 执行测试
        Object result = etlExecutionService.previewResult(testCommand);

        // 验证结果
        assertNotNull(result);
        assertTrue(result instanceof Map);
        
        @SuppressWarnings("unchecked")
        Map<String, Object> resultMap = (Map<String, Object>) result;
        
        assertEquals(2, ((List<?>) resultMap.get("previewData")).size());
        assertEquals(3, resultMap.get("totalColumns"));
        assertEquals(2, resultMap.get("previewRows"));

        // 验证Mock调用
        verify(dorisJdbcTemplate, times(1)).queryForList(anyString());
    }

    @Test
    void testValidateEtlConfig_Success() {
        // 执行测试
        boolean result = etlExecutionService.validateEtlConfig(testCommand);

        // 验证结果
        assertTrue(result);
    }

    @Test
    void testValidateEtlConfig_EmptyTaskName() {
        // 准备测试数据
        testCommand.setTaskName("");

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            etlExecutionService.validateEtlConfig(testCommand);
        });

        assertTrue(exception.getMessage().contains("任务名称不能为空"));
    }

    @Test
    void testValidateEtlConfig_NullSourceLayer() {
        // 准备测试数据
        testCommand.setSourceLayer(null);

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            etlExecutionService.validateEtlConfig(testCommand);
        });

        assertTrue(exception.getMessage().contains("源层级和目标层级不能为空"));
    }

    @Test
    void testGetFieldMappingSuggestions_Success() {
        // 准备Mock数据
        List<Map<String, Object>> mockFields = Arrays.asList(
            createMockField("id", "BIGINT", false, null, "主键ID"),
            createMockField("name", "VARCHAR", true, null, "姓名"),
            createMockField("age", "INT", true, "0", "年龄")
        );

        // 配置Mock行为
        when(dorisExecutionEngine.getTableFields(anyString(), anyString())).thenReturn(mockFields);

        // 执行测试
        Object result = etlExecutionService.getFieldMappingSuggestions("ods_db", "dwd_db", Arrays.asList("test_table"));

        // 验证结果
        assertNotNull(result);
        assertTrue(result instanceof Map);
        
        @SuppressWarnings("unchecked")
        Map<String, Object> resultMap = (Map<String, Object>) result;
        
        assertNotNull(resultMap.get("suggestions"));
        assertEquals("ods_db", resultMap.get("sourceLayer"));
        assertEquals("dwd_db", resultMap.get("targetLayer"));

        // 验证Mock调用
        verify(dorisExecutionEngine, times(1)).getTableFields("ods_db", "test_table");
    }

    @Test
    void testGetExecutionLog_Success() {
        // 准备Mock数据
        String executionId = "test-execution-id";
        EtlExecutionHistoryEntity mockHistory = new EtlExecutionHistoryEntity();
        mockHistory.setExecutionId(executionId);
        mockHistory.setTaskName("测试任务");
        mockHistory.setStatus("SUCCESS");
        mockHistory.setExecutionLog("执行成功");

        // 配置Mock行为
        when(executionHistoryMapper.selectByExecutionId(executionId)).thenReturn(mockHistory);

        // 执行测试
        String result = etlExecutionService.getExecutionLog(executionId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.contains("执行ID: " + executionId));
        assertTrue(result.contains("任务名称: 测试任务"));
        assertTrue(result.contains("执行状态: SUCCESS"));

        // 验证Mock调用
        verify(executionHistoryMapper, times(1)).selectByExecutionId(executionId);
    }

    @Test
    void testGetExecutionMetrics_Success() {
        // 准备Mock数据
        String executionId = "test-execution-id";
        EtlExecutionHistoryEntity mockHistory = new EtlExecutionHistoryEntity();
        mockHistory.setExecutionId(executionId);
        mockHistory.setTaskId(1L);
        mockHistory.setTaskName("测试任务");
        mockHistory.setStatus("SUCCESS");
        mockHistory.setProcessedRows(1000L);
        mockHistory.setExecutionTime(5000L);

        // 配置Mock行为
        when(executionHistoryMapper.selectByExecutionId(executionId)).thenReturn(mockHistory);

        // 执行测试
        Object result = etlExecutionService.getExecutionMetrics(executionId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result instanceof Map);
        
        @SuppressWarnings("unchecked")
        Map<String, Object> resultMap = (Map<String, Object>) result;
        
        assertEquals(executionId, resultMap.get("executionId"));
        assertEquals(1L, resultMap.get("taskId"));
        assertEquals("测试任务", resultMap.get("taskName"));
        assertEquals("SUCCESS", resultMap.get("executionStatus"));
        assertEquals(1000L, resultMap.get("processedRows"));
        assertEquals(5000L, resultMap.get("executionTime"));

        // 验证Mock调用
        verify(executionHistoryMapper, times(1)).selectByExecutionId(executionId);
    }

    @Test
    void testIsTaskRunning_True() {
        // 准备测试数据
        Long taskId = 1L;
        
        // 先提交一个任务使其处于运行状态
        when(etlSqlGenerator.generateSql(any())).thenReturn(Arrays.asList("SELECT 1"));
        when(executionHistoryMapper.insert(any())).thenReturn(1);
        
        etlExecutionService.submitTask(testTaskModel);

        // 执行测试
        boolean result = etlExecutionService.isTaskRunning(taskId);

        // 验证结果
        assertTrue(result);
    }

    @Test
    void testIsTaskRunning_False() {
        // 执行测试
        boolean result = etlExecutionService.isTaskRunning(999L);

        // 验证结果
        assertFalse(result);
    }

    /**
     * 创建Mock行数据
     */
    private Map<String, Object> createMockRow(String id, String name, String age) {
        Map<String, Object> row = new HashMap<>();
        row.put("id", id);
        row.put("name", name);
        row.put("age", age);
        return row;
    }

    /**
     * 创建Mock字段数据
     */
    private Map<String, Object> createMockField(String columnName, String dataType, boolean nullable, String defaultValue, String comment) {
        Map<String, Object> field = new HashMap<>();
        field.put("column_name", columnName);
        field.put("data_type", dataType);
        field.put("is_nullable", nullable ? "YES" : "NO");
        field.put("column_default", defaultValue);
        field.put("column_comment", comment);
        return field;
    }
}
