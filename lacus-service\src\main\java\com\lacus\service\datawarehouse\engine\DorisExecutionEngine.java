package com.lacus.service.datawarehouse.engine;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Doris执行引擎
 * 负责执行SQL语句并记录执行结果
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class DorisExecutionEngine {
    
    @Autowired
    private JdbcTemplate dorisJdbcTemplate;
    
    /**
     * 执行SQL语句列表
     * 
     * @param sqlStatements SQL语句列表
     * @return 执行结果
     */
    public ExecutionResult executeStatements(List<String> sqlStatements) {
        ExecutionResult result = new ExecutionResult();
        result.setStartTime(LocalDateTime.now());
        result.setStatus("RUNNING");
        
        long totalProcessedRows = 0;
        long totalExecutionTime = 0;
        
        try {
            log.info("开始执行SQL语句，数量: {}", sqlStatements.size());
            
            for (int i = 0; i < sqlStatements.size(); i++) {
                String sql = sqlStatements.get(i);
                log.info("执行第{}条SQL: {}", i + 1, sql.substring(0, Math.min(sql.length(), 100)) + "...");
                
                // 执行单条SQL
                SqlExecutionResult sqlResult = executeSingleSql(sql, i + 1);
                
                totalProcessedRows += sqlResult.getAffectedRows();
                totalExecutionTime += sqlResult.getExecutionTime();
                
                result.addSqlResult(sqlResult);
            }
            
            result.setStatus("SUCCESS");
            result.setEndTime(LocalDateTime.now());
            result.setProcessedRows(totalProcessedRows);
            result.setExecutionTimeMs(totalExecutionTime);
            
            log.info("SQL执行完成，处理行数: {}, 总耗时: {}ms", totalProcessedRows, totalExecutionTime);
            
        } catch (Exception e) {
            log.error("SQL执行失败", e);
            result.setEndTime(LocalDateTime.now());
            result.setStatus("FAILED");
            result.setErrorMessage(e.getMessage());
            result.setExecutionTimeMs(totalExecutionTime);
            throw new RuntimeException("SQL执行失败: " + e.getMessage(), e);
        }
        
        return result;
    }
    
    /**
     * 执行单条SQL
     */
    private SqlExecutionResult executeSingleSql(String sql, int sqlIndex) {
        SqlExecutionResult result = new SqlExecutionResult();
        long startTime = System.currentTimeMillis();
        
        try {
            // 执行SQL
            int affectedRows = dorisJdbcTemplate.update(sql);
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;
            
            result.setSqlIndex(sqlIndex);
            result.setSqlContent(sql);
            result.setAffectedRows(affectedRows);
            result.setExecutionTime(executionTime);
            result.setSuccess(true);
            
            log.info("SQL执行成功，影响行数: {}, 耗时: {}ms", affectedRows, executionTime);
            
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;
            
            result.setSqlIndex(sqlIndex);
            result.setSqlContent(sql);
            result.setAffectedRows(0);
            result.setExecutionTime(executionTime);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            
            log.error("SQL执行失败，耗时: {}ms, 错误: {}", executionTime, e.getMessage());
            throw e;
        }
        
        return result;
    }
    
    /**
     * 测试数据库连接
     */
    public boolean testConnection() {
        try {
            dorisJdbcTemplate.queryForObject("SELECT 1", Integer.class);
            return true;
        } catch (Exception e) {
            log.error("测试Doris连接失败", e);
            return false;
        }
    }
    
    /**
     * 获取表行数
     */
    public long getTableRowCount(String database, String tableName) {
        try {
            String sql = "SELECT COUNT(*) FROM " + database + "." + tableName;
            return dorisJdbcTemplate.queryForObject(sql, Long.class);
        } catch (Exception e) {
            log.error("获取表行数失败: {}.{}", database, tableName, e);
            return 0;
        }
    }
    
    /**
     * 检查表是否存在
     */
    public boolean tableExists(String database, String tableName) {
        try {
            String sql = "SELECT COUNT(*) FROM information_schema.tables " +
                        "WHERE table_schema = ? AND table_name = ?";
            Integer count = dorisJdbcTemplate.queryForObject(sql, Integer.class, database, tableName);
            return count != null && count > 0;
        } catch (Exception e) {
            log.error("检查表是否存在失败: {}.{}", database, tableName, e);
            return false;
        }
    }
    
    /**
     * 获取表字段信息
     */
    public List<Map<String, Object>> getTableFields(String database, String tableName) {
        try {
            String sql = "SELECT column_name, data_type, is_nullable, column_default, column_comment " +
                        "FROM information_schema.columns " +
                        "WHERE table_schema = ? AND table_name = ? " +
                        "ORDER BY ordinal_position";
            
            return dorisJdbcTemplate.queryForList(sql, database, tableName);
        } catch (Exception e) {
            log.error("获取表字段信息失败: {}.{}", database, tableName, e);
            return List.of();
        }
    }
    
    /**
     * 执行查询SQL
     */
    public List<Map<String, Object>> executeQuery(String sql) {
        try {
            return dorisJdbcTemplate.queryForList(sql);
        } catch (Exception e) {
            log.error("执行查询SQL失败: {}", sql, e);
            throw new RuntimeException("执行查询SQL失败: " + e.getMessage());
        }
    }
    
    /**
     * 执行结果类
     */
    public static class ExecutionResult {
        private String status;
        private LocalDateTime startTime;
        private LocalDateTime endTime;
        private long processedRows;
        private long executionTimeMs;
        private String errorMessage;
        private List<SqlExecutionResult> sqlResults = List.of();
        
        // getters and setters
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        
        public LocalDateTime getStartTime() { return startTime; }
        public void setStartTime(LocalDateTime startTime) { this.startTime = startTime; }
        
        public LocalDateTime getEndTime() { return endTime; }
        public void setEndTime(LocalDateTime endTime) { this.endTime = endTime; }
        
        public long getProcessedRows() { return processedRows; }
        public void setProcessedRows(long processedRows) { this.processedRows = processedRows; }
        
        public long getExecutionTimeMs() { return executionTimeMs; }
        public void setExecutionTimeMs(long executionTimeMs) { this.executionTimeMs = executionTimeMs; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        
        public List<SqlExecutionResult> getSqlResults() { return sqlResults; }
        public void setSqlResults(List<SqlExecutionResult> sqlResults) { this.sqlResults = sqlResults; }
        
        public void addSqlResult(SqlExecutionResult sqlResult) {
            if (this.sqlResults.isEmpty()) {
                this.sqlResults = new java.util.ArrayList<>();
            }
            this.sqlResults.add(sqlResult);
        }
    }
    
    /**
     * SQL执行结果类
     */
    public static class SqlExecutionResult {
        private int sqlIndex;
        private String sqlContent;
        private int affectedRows;
        private long executionTime;
        private boolean success;
        private String errorMessage;
        
        // getters and setters
        public int getSqlIndex() { return sqlIndex; }
        public void setSqlIndex(int sqlIndex) { this.sqlIndex = sqlIndex; }
        
        public String getSqlContent() { return sqlContent; }
        public void setSqlContent(String sqlContent) { this.sqlContent = sqlContent; }
        
        public int getAffectedRows() { return affectedRows; }
        public void setAffectedRows(int affectedRows) { this.affectedRows = affectedRows; }
        
        public long getExecutionTime() { return executionTime; }
        public void setExecutionTime(long executionTime) { this.executionTime = executionTime; }
        
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    }
}
