package com.lacus.service.datawarehouse;

import com.baomidou.mybatisplus.extension.service.IService;
import com.lacus.dao.datawarehouse.entity.EtlExecutionHistoryEntity;
import com.lacus.dao.datawarehouse.entity.EtlTaskEntity;
import com.lacus.service.datawarehouse.command.EtlTaskAddCommand;
import com.lacus.service.datawarehouse.model.EtlTaskModel;

import java.util.List;

/**
 * ETL执行服务接口
 */
public interface EtlExecutionService  extends IService<EtlExecutionHistoryEntity> {

    /**EtlExecutionService
     * 提交任务执行
     */
    String submitTask(EtlTaskModel model);

    /**
     * 停止任务执行
     */
    void stopTask(Long taskId);

    /**
     * 检查任务是否正在运行
     */
    boolean isTaskRunning(Long taskId);

    /**
     * 预览ETL结果
     */
    Object previewResult(EtlTaskAddCommand command);

    /**
     * 获取字段映射建议
     */
    Object getFieldMappingSuggestions(String sourceLayer, String targetLayer, List<String> sourceTables);


    Object generateFieldMappingsByTargetTable(String sourceLayer, String targetLayer, List<String> sourceTables,String targetTable);
    /**
     * 获取执行日志
     */
    String getExecutionLog(String executionId);

    /**
     * 获取执行指标
     */
    Object getExecutionMetrics(String executionId);

    /**
     * 验证ETL配置
     */
    boolean validateEtlConfig(EtlTaskAddCommand command);

    /**
     * 获取数据血缘关系
     */
    Object getDataLineage(Long taskId);

    /**
     * 获取任务依赖关系
     */
    Object getTaskDependencies(Long taskId);
}
