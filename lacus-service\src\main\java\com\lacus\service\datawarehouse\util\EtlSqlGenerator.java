package com.lacus.service.datawarehouse.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * ETL SQL生成器
 * 根据ETL任务配置生成对应的SQL语句
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class EtlSqlGenerator {
    
    @Autowired
    private ObjectMapper objectMapper;
    
    /**
     * 生成ETL SQL语句
     * 
     * @param taskConfig ETL任务配置
     * @return SQL语句列表
     */
    public List<String> generateSql(Map<String, Object> taskConfig) {
        List<String> sqlStatements = new ArrayList<>();
        
        try {
            String sourceLayer = (String) taskConfig.get("sourceLayer");
            String targetLayer = (String) taskConfig.get("targetLayer");
            String targetTable = (String) taskConfig.get("targetTable");
            String writeMode = (String) taskConfig.get("writeMode");
            
            // 解析源表配置
            List<Map<String, Object>> sourceTables = parseSourceTables(taskConfig);
            
            // 解析字段映射配置
            List<Map<String, Object>> fieldMappings = parseFieldMappings(taskConfig);
            
            // 根据写入模式生成不同的SQL
            switch (writeMode.toUpperCase()) {
                case "OVERWRITE":
                    // 覆盖模式：先删除后插入
                    sqlStatements.add(generateDeleteSql(targetLayer, targetTable));
                    sqlStatements.add(generateInsertSql(sourceLayer, targetLayer, targetTable, sourceTables, fieldMappings));
                    break;
                case "APPEND":
                    // 追加模式：直接插入
                    sqlStatements.add(generateInsertSql(sourceLayer, targetLayer, targetTable, sourceTables, fieldMappings));
                    break;
                case "UPSERT":
                    // 更新插入模式
                    sqlStatements.add(generateUpsertSql(sourceLayer, targetLayer, targetTable, sourceTables, fieldMappings, taskConfig));
                    break;
                default:
                    throw new RuntimeException("不支持的写入模式: " + writeMode);
            }
            
            log.info("生成ETL SQL成功，共{}条语句", sqlStatements.size());
            return sqlStatements;
            
        } catch (Exception e) {
            log.error("生成ETL SQL失败", e);
            throw new RuntimeException("生成ETL SQL失败: " + e.getMessage());
        }
    }
    
    /**
     * 生成DELETE SQL
     */
    private String generateDeleteSql(String targetLayer, String targetTable) {
        return String.format("DELETE FROM %s.%s", targetLayer, targetTable);
    }
    
    /**
     * 生成INSERT SQL
     */
    private String generateInsertSql(String sourceLayer, String targetLayer, String targetTable,
                                   List<Map<String, Object>> sourceTables, 
                                   List<Map<String, Object>> fieldMappings) {
        StringBuilder sql = new StringBuilder();
        
        // INSERT INTO 子句
        sql.append("INSERT INTO ").append(targetLayer).append(".").append(targetTable);
        
        // 字段列表
        sql.append(" (");
        List<String> targetFields = new ArrayList<>();
        for (Map<String, Object> mapping : fieldMappings) {
            targetFields.add((String) mapping.get("targetField"));
        }
        // 添加系统字段
        targetFields.add("etl_time");
        targetFields.add("data_version");
        sql.append(String.join(", ", targetFields));
        sql.append(")");
        
        // SELECT 子句
        sql.append(" SELECT ");
        List<String> selectFields = new ArrayList<>();
        for (Map<String, Object> mapping : fieldMappings) {
            String sourceField = (String) mapping.get("sourceField");
            String expression = (String) mapping.get("expression");
            
            if (expression != null && !expression.trim().isEmpty()) {
                selectFields.add(expression);
            } else {
                selectFields.add(sourceField);
            }
        }
        // 添加系统字段值
        selectFields.add("NOW()");
        selectFields.add("UNIX_TIMESTAMP()");
        sql.append(String.join(", ", selectFields));
        
        // FROM 子句
        sql.append(" FROM ");
        if (sourceTables.size() == 1) {
            Map<String, Object> sourceTable = sourceTables.get(0);
            sql.append(sourceLayer).append(".").append(sourceTable.get("tableName"));
        } else {
            // 多表关联
            sql.append(generateJoinClause(sourceLayer, sourceTables));
        }
        
        // WHERE 子句（如果有过滤条件）
        String whereClause = generateWhereClause(sourceTables);
        if (!whereClause.isEmpty()) {
            sql.append(" WHERE ").append(whereClause);
        }
        
        return sql.toString();
    }
    
    /**
     * 生成UPSERT SQL
     */
    private String generateUpsertSql(String sourceLayer, String targetLayer, String targetTable,
                                   List<Map<String, Object>> sourceTables, 
                                   List<Map<String, Object>> fieldMappings,
                                   Map<String, Object> taskConfig) {
        StringBuilder sql = new StringBuilder();
        
        // 使用INSERT ... ON DUPLICATE KEY UPDATE语法
        sql.append(generateInsertSql(sourceLayer, targetLayer, targetTable, sourceTables, fieldMappings));
        
        // ON DUPLICATE KEY UPDATE 子句
        sql.append(" ON DUPLICATE KEY UPDATE ");
        List<String> updateFields = new ArrayList<>();
        for (Map<String, Object> mapping : fieldMappings) {
            String targetField = (String) mapping.get("targetField");
            // 跳过主键字段
            if (!isPrimaryKey(targetField, taskConfig)) {
                updateFields.add(targetField + " = VALUES(" + targetField + ")");
            }
        }
        updateFields.add("etl_time = NOW()");
        updateFields.add("data_version = UNIX_TIMESTAMP()");
        sql.append(String.join(", ", updateFields));
        
        return sql.toString();
    }
    
    /**
     * 生成JOIN子句
     */
    private String generateJoinClause(String sourceLayer, List<Map<String, Object>> sourceTables) {
        StringBuilder joinClause = new StringBuilder();
        
        for (int i = 0; i < sourceTables.size(); i++) {
            Map<String, Object> sourceTable = sourceTables.get(i);
            String tableName = (String) sourceTable.get("tableName");
            String alias = (String) sourceTable.get("alias");
            
            if (i == 0) {
                // 主表
                joinClause.append(sourceLayer).append(".").append(tableName);
                if (alias != null && !alias.isEmpty()) {
                    joinClause.append(" ").append(alias);
                }
            } else {
                // 关联表
                String joinType = (String) sourceTable.get("joinType");
                String joinCondition = (String) sourceTable.get("joinCondition");
                
                if (joinType == null) {
                    joinType = "INNER JOIN";
                }
                
                joinClause.append(" ").append(joinType).append(" ");
                joinClause.append(sourceLayer).append(".").append(tableName);
                if (alias != null && !alias.isEmpty()) {
                    joinClause.append(" ").append(alias);
                }
                
                if (joinCondition != null && !joinCondition.isEmpty()) {
                    joinClause.append(" ON ").append(joinCondition);
                }
            }
        }
        
        return joinClause.toString();
    }
    
    /**
     * 生成WHERE子句
     */
    private String generateWhereClause(List<Map<String, Object>> sourceTables) {
        List<String> conditions = new ArrayList<>();
        
        for (Map<String, Object> sourceTable : sourceTables) {
            String filterCondition = (String) sourceTable.get("filterCondition");
            if (filterCondition != null && !filterCondition.trim().isEmpty()) {
                conditions.add("(" + filterCondition + ")");
            }
        }
        
        return String.join(" AND ", conditions);
    }
    
    /**
     * 解析源表配置
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> parseSourceTables(Map<String, Object> taskConfig) {
        try {
            Object sourceTablesConfig = taskConfig.get("sourceTablesConfig");
            if (sourceTablesConfig instanceof String) {
                return objectMapper.readValue((String) sourceTablesConfig, List.class);
            } else if (sourceTablesConfig instanceof List) {
                return (List<Map<String, Object>>) sourceTablesConfig;
            }
            return new ArrayList<>();
        } catch (Exception e) {
            throw new RuntimeException("解析源表配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 解析字段映射配置
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> parseFieldMappings(Map<String, Object> taskConfig) {
        try {
            Object fieldMappingsConfig = taskConfig.get("fieldMappingsConfig");
            if (fieldMappingsConfig instanceof String) {
                return objectMapper.readValue((String) fieldMappingsConfig, List.class);
            } else if (fieldMappingsConfig instanceof List) {
                return (List<Map<String, Object>>) fieldMappingsConfig;
            }
            return new ArrayList<>();
        } catch (Exception e) {
            throw new RuntimeException("解析字段映射配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 判断是否为主键字段
     */
    private boolean isPrimaryKey(String fieldName, Map<String, Object> taskConfig) {
        Object primaryKeys = taskConfig.get("primaryKeys");
        if (primaryKeys instanceof String) {
            return ((String) primaryKeys).contains(fieldName);
        } else if (primaryKeys instanceof List) {
            return ((List<?>) primaryKeys).contains(fieldName);
        }
        return false;
    }
}
