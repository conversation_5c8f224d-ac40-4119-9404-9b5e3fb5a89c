# Doris配置验证指南

## 配置总结

您的Doris数据库配置已经添加到 `lacus-core/src/main/resources/application-dev.yml` 中：

```yaml
# Doris数据源配置
spring:
  datasource:
    doris:
      type: com.alibaba.druid.pool.DruidDataSource
      driverClassName: com.mysql.cj.jdbc.Driver
      url: **********************************************************************************************************************************************
      username: root
      password: 123456
      # 连接池配置...

# 数据仓库业务配置
datawarehouse:
  doris:
    databases:
      ods: ods_db
      dwd: dwd_db
      dws: dws_db
      ads: ads_db
```

## 验证步骤

### 1. 启动应用验证连接

启动应用后，查看日志输出：

```bash
# 查看连接测试日志
tail -f logs/application.log | grep -E "(<PERSON>|数据库连接)"
```

期望看到的日志：
```
✅ Doris数据库连接成功
📊 可用的数据库列表:
  - information_schema
  - mysql
  - ods_db
  - dwd_db
  - dws_db
  - ads_db
```

### 2. 手动验证Doris连接

如果应用连接失败，可以手动测试：

```bash
# 使用MySQL客户端连接Doris
mysql -h 192.168.10.35 -P 9030 -u root -p123456

# 连接成功后执行
SHOW DATABASES;
```

### 3. 创建必要的数据库

如果数据库不存在，执行以下SQL：

```sql
-- 创建数据仓库相关数据库
CREATE DATABASE IF NOT EXISTS ods_db;
CREATE DATABASE IF NOT EXISTS dwd_db;
CREATE DATABASE IF NOT EXISTS dws_db;
CREATE DATABASE IF NOT EXISTS ads_db;

-- 验证创建结果
SHOW DATABASES;
```

### 4. 创建测试表

为了测试智能映射功能，创建一个测试表：

```sql
-- 使用ods_db数据库
USE ods_db;

-- 创建用户信息表
CREATE TABLE IF NOT EXISTS ods_user_info (
    id BIGINT NOT NULL COMMENT '用户ID',
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    age INT COMMENT '年龄',
    gender VARCHAR(10) COMMENT '性别',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=OLAP
DUPLICATE KEY(id)
DISTRIBUTED BY HASH(id) BUCKETS 10
PROPERTIES (
    "replication_num" = "1"
);

-- 插入测试数据
INSERT INTO ods_user_info (id, username, email, phone, age, gender) VALUES
(1, 'user1', '<EMAIL>', '13800138001', 25, '男'),
(2, 'user2', '<EMAIL>', '13800138002', 30, '女'),
(3, 'user3', '<EMAIL>', '13800138003', 28, '男'),
(4, 'user4', '<EMAIL>', '13800138004', 32, '女'),
(5, 'user5', '<EMAIL>', '13800138005', 27, '男');

-- 验证数据
SELECT * FROM ods_user_info;
```

## 常见问题排查

### 1. 连接超时

**错误信息**: `Connection timed out`

**解决方案**:
- 检查Doris服务是否启动：`ps aux | grep doris`
- 检查端口是否开放：`telnet 192.168.10.35 9030`
- 检查防火墙设置

### 2. 认证失败

**错误信息**: `Access denied for user 'root'@'%'`

**解决方案**:
- 检查用户名密码是否正确
- 在Doris中创建用户并授权：
```sql
-- 连接到Doris管理端
mysql -h 192.168.10.35 -P 9030 -u root -p

-- 创建用户（如果需要）
CREATE USER 'lacus'@'%' IDENTIFIED BY '123456';

-- 授权
GRANT ALL PRIVILEGES ON *.* TO 'lacus'@'%';
FLUSH PRIVILEGES;
```

### 3. 数据库不存在

**错误信息**: `Unknown database 'ods_db'`

**解决方案**:
- 执行上面的创建数据库SQL语句
- 或者在应用中调用 `DorisConnectionTest.createTestTableIfNotExists()` 方法

### 4. 智能映射返回空字段

**可能原因**:
- 表不存在
- 表没有字段
- 权限不足

**排查步骤**:
1. 检查表是否存在：
```sql
SHOW TABLES FROM ods_db LIKE 'ods_user_info';
```

2. 检查表结构：
```sql
DESC ods_db.ods_user_info;
```

3. 检查information_schema权限：
```sql
SELECT * FROM information_schema.columns 
WHERE table_schema = 'ods_db' AND table_name = 'ods_user_info';
```

## 测试智能映射功能

配置完成后，测试智能映射接口：

```bash
curl -X POST http://localhost:8080/lacus-api/datawarehouse/etl/mapping-suggestions \
  -H "Content-Type: application/json" \
  -d '{
    "sourceLayer": "ODS",
    "targetLayer": "DWD", 
    "sourceTables": ["ods_user_info"]
  }'
```

期望返回结果包含：
- `sourceFields`: 源表字段列表（不为空）
- `suggestedTargetFields`: 建议的目标字段
- `fieldMappings`: 字段映射建议

## 性能优化建议

### 1. 连接池配置

根据实际负载调整连接池参数：

```yaml
spring:
  datasource:
    doris:
      initialSize: 5        # 初始连接数
      minIdle: 5           # 最小空闲连接
      maxActive: 20        # 最大活跃连接
      maxWait: 60000       # 最大等待时间
```

### 2. 查询优化

- 使用合适的分区和分桶策略
- 为常用查询字段创建索引
- 定期分析表统计信息

### 3. 监控配置

添加连接池监控：

```yaml
spring:
  datasource:
    doris:
      druid:
        stat-view-servlet:
          enabled: true
          url-pattern: /druid/*
```

访问 `http://localhost:8080/druid` 查看连接池状态。

## 下一步操作

1. **启动应用**: 确保所有配置正确加载
2. **查看日志**: 验证Doris连接是否成功
3. **创建测试数据**: 运行上面的SQL脚本
4. **测试接口**: 验证智能映射和预览功能
5. **监控性能**: 观察连接池和查询性能

如果遇到问题，请检查：
- 网络连接
- Doris服务状态
- 用户权限
- 防火墙设置
- 应用日志
