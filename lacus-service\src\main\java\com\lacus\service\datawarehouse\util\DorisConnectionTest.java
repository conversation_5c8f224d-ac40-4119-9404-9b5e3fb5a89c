package com.lacus.service.datawarehouse.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;

/**
 * Doris连接测试工具
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class DorisConnectionTest {

    @Autowired
    @Qualifier("dorisJdbcTemplate")
    private JdbcTemplate dorisJdbcTemplate;

    /**
     * 应用启动后测试Doris连接
     */
    @PostConstruct
    public void testConnection() {
        try {
            log.info("开始测试Doris数据库连接...");
            
            // 测试基本连接
            Integer result = dorisJdbcTemplate.queryForObject("SELECT 1", Integer.class);
            if (result != null && result == 1) {
                log.info("✅ Doris数据库连接成功");
            }
            
            // 查看可用的数据库
            List<Map<String, Object>> databases = dorisJdbcTemplate.queryForList("SHOW DATABASES");
            log.info("📊 可用的数据库列表:");
            for (Map<String, Object> db : databases) {
                log.info("  - {}", db.get("Database"));
            }
            
            // 检查数据仓库相关数据库是否存在
            checkDatabase("ods_db");
            checkDatabase("dwd_db");
            checkDatabase("dws_db");
            checkDatabase("ads_db");
            
        } catch (Exception e) {
            log.error("❌ Doris数据库连接失败", e);
            log.error("请检查以下配置:");
            log.error("1. Doris服务是否启动 (192.168.10.35:9030)");
            log.error("2. 用户名密码是否正确 (root/123456)");
            log.error("3. 网络连接是否正常");
            log.error("4. 防火墙设置是否正确");
        }
    }

    /**
     * 检查指定数据库是否存在
     */
    private void checkDatabase(String databaseName) {
        try {
            List<Map<String, Object>> result = dorisJdbcTemplate.queryForList(
                "SELECT SCHEMA_NAME FROM information_schema.SCHEMATA WHERE SCHEMA_NAME = ?", 
                databaseName
            );
            
            if (result.isEmpty()) {
                log.warn("⚠️  数据库 {} 不存在，建议创建", databaseName);
                log.info("创建命令: CREATE DATABASE IF NOT EXISTS {};", databaseName);
            } else {
                log.info("✅ 数据库 {} 存在", databaseName);
                
                // 查看表数量
                List<Map<String, Object>> tables = dorisJdbcTemplate.queryForList(
                    "SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = ?", 
                    databaseName
                );
                
                if (!tables.isEmpty()) {
                    Object tableCount = tables.get(0).get("table_count");
                    log.info("  📋 包含 {} 个表", tableCount);
                }
            }
        } catch (Exception e) {
            log.error("检查数据库 {} 时出错: {}", databaseName, e.getMessage());
        }
    }

    /**
     * 手动测试连接方法
     */
    public boolean testDorisConnection() {
        try {
            Integer result = dorisJdbcTemplate.queryForObject("SELECT 1", Integer.class);
            return result != null && result == 1;
        } catch (Exception e) {
            log.error("测试Doris连接失败", e);
            return false;
        }
    }

    /**
     * 获取表字段信息（用于调试）
     */
    public List<Map<String, Object>> getTableFields(String database, String tableName) {
        try {
            String sql = "SELECT column_name, data_type, is_nullable, column_default, column_comment " +
                        "FROM information_schema.columns " +
                        "WHERE table_schema = ? AND table_name = ? " +
                        "ORDER BY ordinal_position";
            
            List<Map<String, Object>> fields = dorisJdbcTemplate.queryForList(sql, database, tableName);
            log.info("表 {}.{} 包含 {} 个字段", database, tableName, fields.size());
            
            for (Map<String, Object> field : fields) {
                log.debug("字段: {} - {} - {}", 
                    field.get("column_name"), 
                    field.get("data_type"), 
                    field.get("column_comment"));
            }
            
            return fields;
            
        } catch (Exception e) {
            log.error("获取表字段信息失败: {}.{}", database, tableName, e);
            return null;
        }
    }

    /**
     * 创建测试表（如果不存在）
     */
    public void createTestTableIfNotExists() {
        try {
            // 创建测试数据库
            dorisJdbcTemplate.execute("CREATE DATABASE IF NOT EXISTS ods_db");
            dorisJdbcTemplate.execute("CREATE DATABASE IF NOT EXISTS dwd_db");
            dorisJdbcTemplate.execute("CREATE DATABASE IF NOT EXISTS dws_db");
            dorisJdbcTemplate.execute("CREATE DATABASE IF NOT EXISTS ads_db");
            
            // 创建测试表
            String createTableSql = "                CREATE TABLE IF NOT EXISTS ods_db.ods_user_info (\n" +
                    "                    id BIGINT NOT NULL COMMENT '用户ID',\n" +
                    "                    username VARCHAR(50) NOT NULL COMMENT '用户名',\n" +
                    "                    email VARCHAR(100) COMMENT '邮箱',\n" +
                    "                    phone VARCHAR(20) COMMENT '手机号',\n" +
                    "                    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',\n" +
                    "                    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'\n" +
                    "                ) ENGINE=OLAP\n" +
                    "                DUPLICATE KEY(id)\n" +
                    "                DISTRIBUTED BY HASH(id) BUCKETS 10\n" +
                    "                PROPERTIES (\n" +
                    "                    \"replication_num\" = \"1\"\n" +
                    "                )";
            
            dorisJdbcTemplate.execute(createTableSql);
            log.info("✅ 测试表 ods_db.ods_user_info 创建成功");
            
            // 插入测试数据
            String insertSql = "INSERT INTO ods_db.ods_user_info (id, username, email, phone) VALUES\n" +
                    "                (1, 'user1', '<EMAIL>', '13800138001'),\n" +
                    "                (2, 'user2', '<EMAIL>', '13800138002'),\n" +
                    "                (3, 'user3', '<EMAIL>', '13800138003')";
            
            dorisJdbcTemplate.execute(insertSql);
            log.info("✅ 测试数据插入成功");
            
        } catch (Exception e) {
            log.error("创建测试表失败", e);
        }
    }
}
