package com.lacus.dao.datawarehouse.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * ETL执行历史表
 */
@Getter
@Setter
@TableName("etl_execution_history")
@ApiModel(value = "EtlExecutionHistoryEntity对象", description = "ETL执行历史表")
public class EtlExecutionHistoryEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("执行ID")
    @TableId("execution_id")
    private String executionId;

    @ApiModelProperty("任务ID")
    @TableField("task_id")
    private Long taskId;

    @ApiModelProperty("任务名称")
    @TableField("task_name")
    private String taskName;

    @ApiModelProperty("执行状态")
    @TableField("status")
    private String status;

    @ApiModelProperty("执行状态（新字段）")
    @TableField("execution_status")
    private String executionStatus;

    @ApiModelProperty("开始时间")
    @TableField("start_time")
    private Date startTime;

    @ApiModelProperty("结束时间")
    @TableField("end_time")
    private Date endTime;

    @ApiModelProperty("执行时长")
    @TableField("duration")
    private Long duration;

    @ApiModelProperty("执行时间（毫秒）")
    @TableField("execution_time")
    private Long executionTime;

    @ApiModelProperty("处理记录数")
    @TableField("processed_records")
    private Long processedRecords;

    @ApiModelProperty("处理行数")
    @TableField("processed_rows")
    private Long processedRows;

    @ApiModelProperty("错误记录数")
    @TableField("error_records")
    private Long errorRecords;

    @ApiModelProperty("错误信息")
    @TableField("error_message")
    private String errorMessage;

    @ApiModelProperty("执行日志路径")
    @TableField("log_path")
    private String logPath;

    @ApiModelProperty("执行参数")
    @TableField("execution_params")
    private String executionParams;

    @ApiModelProperty("执行日志")
    @TableField("execution_log")
    private String executionLog;

    @ApiModelProperty("创建时间")
    @TableField("create_time")
    private Date createTime;
}
