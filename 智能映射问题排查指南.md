# 智能映射问题排查指南

## 问题描述

1. **智能映射返回sourceFields为空**：调用 `/mapping-suggestions` 接口返回的 `sourceFields` 数组为空
2. **预览结果报错**：调用预览功能时报"源表配置不能为空"错误

## 问题分析

### 1. 智能映射问题

**可能原因**：
- 数据库连接问题
- 表不存在
- 权限问题
- SQL查询失败

**排查步骤**：

#### 步骤1：检查数据库连接
```bash
# 检查Doris数据库是否可以连接
mysql -h doris_host -P 9030 -u username -p

# 检查数据库是否存在
SHOW DATABASES;

# 检查表是否存在
USE ods_db;
SHOW TABLES LIKE 'ods_user_info';
```

#### 步骤2：检查表结构
```sql
-- 查看表结构
DESC ods_db.ods_user_info;

-- 查看表字段信息（使用information_schema）
SELECT column_name, data_type, is_nullable, column_default, column_comment 
FROM information_schema.columns 
WHERE table_schema = 'ods_db' AND table_name = 'ods_user_info' 
ORDER BY ordinal_position;
```

#### 步骤3：检查应用日志
查看应用日志中的错误信息：
```bash
tail -f logs/application.log | grep -E "(getTableFields|mapping-suggestions|ERROR)"
```

#### 步骤4：测试DorisExecutionEngine
创建一个简单的测试方法：
```java
@Test
public void testGetTableFields() {
    List<Map<String, Object>> fields = dorisExecutionEngine.getTableFields("ods_db", "ods_user_info");
    System.out.println("获取到的字段数量: " + fields.size());
    for (Map<String, Object> field : fields) {
        System.out.println("字段: " + field);
    }
}
```

### 2. 预览结果问题

**问题原因**：预览功能调用了严格的配置验证，要求源表配置不能为空。

**解决方案**：已修改为使用宽松验证的 `validatePreviewConfig` 方法。

## 修复方案

### 1. 修复智能映射问题

#### 方案A：检查数据库配置
确保 `application.yml` 中的Doris配置正确：
```yaml
spring:
  datasource:
    doris:
      url: ****************************/
      username: your_username
      password: your_password
      driver-class-name: com.mysql.cj.jdbc.Driver
```

#### 方案B：添加错误处理和日志
在 `getTableFields` 方法中添加更详细的日志：
```java
public List<Map<String, Object>> getTableFields(String layer, String tableName) {
    try {
        String database = convertLayerToDatabase(layer);
        log.info("获取表字段信息: {}.{}", database, tableName);
        
        // 先检查表是否存在
        if (!dorisExecutionEngine.tableExists(database, tableName)) {
            log.warn("表不存在: {}.{}", database, tableName);
            return new ArrayList<>();
        }
        
        List<Map<String, Object>> fields = dorisExecutionEngine.getTableFields(database, tableName);
        log.info("成功获取{}个字段", fields != null ? fields.size() : 0);
        
        return fields != null ? fields : new ArrayList<>();
        
    } catch (Exception e) {
        log.error("获取表字段信息失败: {}.{}", layer, tableName, e);
        return new ArrayList<>();
    }
}
```

#### 方案C：创建测试表
如果表不存在，创建一个测试表：
```sql
-- 创建测试表
CREATE TABLE ods_db.ods_user_info (
    id BIGINT NOT NULL COMMENT '用户ID',
    username VARCHAR(50) NOT NULL COMMENT '用户名',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=OLAP
DUPLICATE KEY(id)
DISTRIBUTED BY HASH(id) BUCKETS 10
PROPERTIES (
    "replication_num" = "1"
);

-- 插入测试数据
INSERT INTO ods_db.ods_user_info (id, username, email, phone) VALUES
(1, 'user1', '<EMAIL>', '13800138001'),
(2, 'user2', '<EMAIL>', '13800138002'),
(3, 'user3', '<EMAIL>', '13800138003');
```

### 2. 修复预览功能

预览功能已修改为使用宽松验证，现在应该可以正常工作。

## 测试验证

### 1. 测试智能映射
```bash
curl -X POST http://localhost:8080/lacus-api/datawarehouse/etl/mapping-suggestions \
  -H "Content-Type: application/json" \
  -d '{"sourceLayer":"ODS","targetLayer":"DWD","sourceTables":["ods_user_info"]}'
```

期望返回结果包含非空的 `sourceFields` 数组。

### 2. 测试预览功能
```bash
curl -X POST http://localhost:8080/lacus-api/datawarehouse/etl/preview \
  -H "Content-Type: application/json" \
  -d '{"sourceLayer":"ODS","targetLayer":"DWD"}'
```

期望返回预览数据而不是配置验证错误。

## 前端问题排查

### 1. 检查前端调用
确保前端正确处理返回的数据结构：
```javascript
// 智能映射按钮点击事件
handleIntelligentMapping() {
  const params = {
    sourceLayer: this.form.sourceLayer,
    targetLayer: this.form.targetLayer,
    sourceTables: this.form.sourceTables || []
  };
  
  getFieldMappingSuggestions(params).then(response => {
    console.log('智能映射返回结果:', response.data);
    
    if (response.data && response.data.suggestions) {
      const suggestions = response.data.suggestions;
      
      // 处理每个表的建议
      suggestions.forEach(suggestion => {
        console.log('源表:', suggestion.sourceTable);
        console.log('源字段:', suggestion.sourceFields);
        console.log('目标字段:', suggestion.suggestedTargetFields);
        console.log('字段映射:', suggestion.fieldMappings);
        
        // 更新前端表单
        this.updateFieldMappings(suggestion);
      });
    }
  }).catch(error => {
    console.error('智能映射失败:', error);
    this.$message.error('智能映射失败: ' + error.message);
  });
}
```

### 2. 检查数据绑定
确保前端正确绑定和显示数据：
```vue
<template>
  <!-- 字段映射表格 -->
  <el-table :data="fieldMappings" border>
    <el-table-column prop="sourceField" label="源字段" />
    <el-table-column prop="targetField" label="目标字段" />
    <el-table-column prop="dataType" label="数据类型" />
    <el-table-column label="操作">
      <template slot-scope="scope">
        <el-button size="mini" @click="editMapping(scope.row)">编辑</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>
```

## 常见问题解决

### 1. 数据库连接超时
```yaml
# 增加连接超时时间
spring:
  datasource:
    doris:
      connection-timeout: 30000
      socket-timeout: 60000
```

### 2. 权限不足
确保数据库用户有查询 `information_schema` 的权限：
```sql
GRANT SELECT ON information_schema.* TO 'your_username'@'%';
```

### 3. 表名大小写问题
Doris可能对表名大小写敏感，确保表名正确：
```java
// 统一转换为小写
tableName = tableName.toLowerCase();
```

## 监控和日志

### 1. 添加监控指标
```java
@Component
public class EtlMetrics {
    private final MeterRegistry meterRegistry;
    
    public void recordMappingSuggestionRequest(String sourceLayer, String targetLayer, boolean success) {
        Timer.Sample sample = Timer.start(meterRegistry);
        sample.stop(Timer.builder("etl.mapping.suggestion")
            .tag("source.layer", sourceLayer)
            .tag("target.layer", targetLayer)
            .tag("success", String.valueOf(success))
            .register(meterRegistry));
    }
}
```

### 2. 结构化日志
```java
log.info("智能映射请求 - 源层级: {}, 目标层级: {}, 源表: {}, 结果: {} 个建议", 
    sourceLayer, targetLayer, sourceTables, suggestions.size());
```

通过以上排查和修复步骤，应该能够解决智能映射和预览功能的问题。
